# -*- coding: utf-8 -*-
import os
import json
import time
import logging
import requests
from config.config import API_CONFIG

# Get module logger
logger = logging.getLogger(__name__)

class InterfaceAPI:
    """General API interface class supporting multiple LLM models"""

    def __init__(self, api_type="gemini", debug_mode=False):
        """Initialize API interface

        Args:
            api_type (str): API type, supports 'gemini', 'deepseek', 'xfyun', 'zhipu'
            debug_mode (bool): Whether to enable debug mode
        """
        self.api_type = api_type
        self.config = API_CONFIG.get(api_type, {})
        self.logger = logging.getLogger(f"InterfaceAPI.{api_type}")
        self.debug_mode = debug_mode
        self.logger.info(f"Initializing {api_type} API interface")

        # Initialize specific API
        if api_type == "gemini":
            self._init_gemini()
        elif api_type == "deepseek":
            self._init_deepseek()
        elif api_type == "xfyun":
            self._init_xfyun()
        elif api_type == "zhipu":
            self._init_zhipu()
        else:
            self.logger.error(f"Unsupported API type: {api_type}")
            raise ValueError(f"Unsupported API type: {api_type}")
    
    def _init_gemini(self):
        """Initialize Google Gemini API"""
        # Gemini API uses manual requests calls, no special initialization needed
        self.logger.info(f"Gemini API configuration complete, model: {self.config.get('model')}")

    def _init_deepseek(self):
        """Initialize DeepSeek API"""
        # DeepSeek API needs no special initialization, just save configuration
        self.logger.info(f"DeepSeek API configuration complete, model: {self.config.get('model')}")

    def _init_xfyun(self):
        """Initialize iFlytek Spark API"""
        # iFlytek Spark API needs no special initialization, just save configuration
        self.logger.info(f"iFlytek Spark API configuration complete, model: {self.config.get('model')}")

    def _init_zhipu(self):
        """Initialize Zhipu API"""
        # Zhipu API needs no special initialization, just save configuration
        self.logger.info(f"Zhipu API configuration complete, model: {self.config.get('model')}")

    def _make_request(self, url, headers, data, max_retries, retry_delay, api_name, response_parser):
        """
        General request method

        Args:
            url (str): Request URL
            headers (dict): Request headers
            data (dict): Request data
            max_retries (int): Maximum retry attempts
            retry_delay (int): Retry delay
            api_name (str): API name
            response_parser (function): Callback function for parsing response

        Returns:
            str: API response text
        """
        retries = 0
        while retries < max_retries:
            try:
                self.logger.info(f"Sending request to {api_name} API (attempt {retries+1}/{max_retries})")
                response = requests.post(url, headers=headers, json=data, timeout=10)
                response.raise_for_status()
                response_data = response.json()

                # Use callback function to parse response
                return response_parser(response_data)

            except requests.exceptions.RequestException as e:
                self.logger.error(f"{api_name} API network request error: {e}")
                retries += 1
                if retries >= max_retries:
                    return f"API request failed: {e}"
                if self.debug_mode:
                    self.logger.debug(f"Retrying... ({retries}/{max_retries})")
                time.sleep(retry_delay)
            except json.JSONDecodeError as e:
                self.logger.error(f"{api_name} API response parsing error: {e}")
                return f"API response parsing failed: {e}"
            except Exception as e:
                self.logger.error(f"{api_name} API unknown error: {e}")
                retries += 1
                if retries >= max_retries:
                    return f"API request failed: {e}"
                if self.debug_mode:
                    self.logger.debug(f"Retrying... ({retries}/{max_retries})")
                time.sleep(retry_delay)

        self.logger.error("Maximum retry attempts reached, exiting.")
        return "API request failed: Maximum retry attempts reached"

    def _parse_gemini_response(self, response_data):
        if "candidates" in response_data and response_data["candidates"]:
            candidate = response_data["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                return candidate["content"]["parts"][0]["text"]
        
        self.logger.error(f"API response format does not match expectations: {response_data}")
        return f"API response format does not match expectations: {response_data}"

    def _parse_standard_chat_response(self, response_data):
        if "error" in response_data:
            error_msg = response_data.get("error", {}).get("message", "Unknown error")
            self.logger.error(f"API returned error: {error_msg}")
            return f"API request failed: {error_msg}"

        if "choices" in response_data and len(response_data["choices"]) > 0:
            return response_data["choices"][0]["message"]["content"]

        self.logger.error(f"API response format abnormal: {response_data}")
        return "API response format abnormal"

    def get_gemini_response(self, prompt_content, max_retries=3, retry_delay=20):
        """
        Call Google Gemini API to get response
        """
        api_key = self.config.get("api_key")
        model = self.config.get("model")
        api_base = self.config.get("api_base")

        if not all([api_key, model, api_base]):
            self.logger.error("Gemini API configuration incomplete")
            return "Gemini API configuration incomplete"

        url = f"{api_base}/{model}:generateContent?key={api_key}"
        
        headers = { "Content-Type": "application/json" }
        
        data = {
            "contents": [{"parts": [{"text": prompt_content}]}]
        }
        
        return self._make_request(url, headers, data, max_retries, retry_delay, "Gemini", self._parse_gemini_response)

    def get_deepseek_response(self, prompt, max_retries=3, retry_delay=2):
        """Get DeepSeek API response"""
        if self.api_type != "deepseek":
            self.logger.warning(f"Current API type is {self.api_type}, but called get_deepseek_response")

        api_base = self.config.get("api_base")
        api_key = self.config.get("api_key")
        model = self.config.get("model")

        if not all([api_base, api_key, model]):
            self.logger.error("DeepSeek API configuration incomplete")
            return "DeepSeek API configuration incomplete"
        
        url = f"{api_base}/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(url, headers, data, max_retries, retry_delay, "DeepSeek", self._parse_standard_chat_response)

    def get_xfyun_response(self, prompt, max_retries=3, retry_delay=2):
        """Get iFlytek Spark API response"""
        if self.api_type != "xfyun":
            self.logger.warning(f"Current API type is {self.api_type}, but called get_xfyun_response")

        api_endpoint = self.config.get("endpoint")
        api_key = self.config.get("api_key")
        model = self.config.get("model")

        if not all([api_endpoint, api_key, model]):
            self.logger.error("iFlytek Spark API configuration incomplete")
            return "iFlytek Spark API configuration incomplete"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(api_endpoint, headers, data, max_retries, retry_delay, "iFlytek Spark", self._parse_standard_chat_response)

    def _get_zhipu_token(self):
        """Generate Zhipu API authentication token"""
        api_key = self.config.get("api_key")
        if not api_key:
            self.logger.error("Zhipu API key not configured")
            return None, "API key not configured"

        try:
            import jwt
            import datetime

            key_parts = api_key.split('.')
            if len(key_parts) != 2:
                self.logger.error("Zhipu API key format error")
                return None, "API key format error"
            
            api_id, api_secret = key_parts
            
            exp_time = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1)
            payload = {
                "api_key": api_id,
                "exp": int(exp_time.timestamp()),
                "timestamp": int(datetime.datetime.now(datetime.timezone.utc).timestamp())
            }
            return jwt.encode(payload, api_secret, algorithm="HS256"), None
            
        except ImportError:
            msg = "Missing PyJWT library, please install with `pip install PyJWT`"
            self.logger.error(msg)
            return None, msg
        except Exception as e:
            msg = f"Failed to generate Zhipu API authentication token: {e}"
            self.logger.error(msg)
            return None, msg

    def get_zhipu_response(self, prompt, max_retries=3, retry_delay=2):
        """Get Zhipu API response"""
        if self.api_type != "zhipu":
            self.logger.warning(f"Current API type is {self.api_type}, but called get_zhipu_response")

        token, error = self._get_zhipu_token()
        if error:
            return error

        model = self.config.get("model")
        if not model:
            self.logger.error("Zhipu API model not configured")
            return "Zhipu API model not configured"
        
        api_endpoint = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        return self._make_request(api_endpoint, headers, data, max_retries, retry_delay, "Zhipu", self._parse_standard_chat_response)

    def get_response(self, prompt, max_retries=3, retry_delay=2):
        """Get response based on current API type

        Args:
            prompt (str): Prompt text
            max_retries (int): Maximum retry attempts
            retry_delay (int): Retry delay time (seconds)

        Returns:
            str: API response text
        """
        if self.api_type == "gemini":
            return self.get_gemini_response(prompt, max_retries, retry_delay)
        elif self.api_type == "deepseek":
            return self.get_deepseek_response(prompt, max_retries, retry_delay)
        elif self.api_type == "xfyun":
            return self.get_xfyun_response(prompt, max_retries, retry_delay)
        elif self.api_type == "zhipu":
            return self.get_zhipu_response(prompt, max_retries, retry_delay)
        else:
            self.logger.error(f"Unsupported API type: {self.api_type}")
            return f"Unsupported API type: {self.api_type}"