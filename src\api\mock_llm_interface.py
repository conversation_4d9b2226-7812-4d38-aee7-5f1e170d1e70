# -*- coding: utf-8 -*-
"""
Mock LLM Interface
For testing LLM strategy selection functionality without requiring real API keys
"""

import json
import logging
import random
import time
from typing import Dict, List, Any

class MockLLMInterface:
    """Mock LLM interface for testing LLM strategy selection functionality"""

    def __init__(self, response_mode='intelligent', debug_mode=True):
        """
        Initialize mock LLM interface

        Args:
            response_mode: Response mode ('intelligent', 'random', 'fixed', 'error')
            debug_mode: Whether to enable debug mode
        """
        self.response_mode = response_mode
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        self.call_count = 0
        self.response_history = []

        # Strategy type mapping
        self.strategy_types = [
            "STRONG_EXPLORATION",
            "INTELLIGENT_EXPLORATION",
            "BALANCED_EXPLORATION",
            "MODERATE_EXPLOITATION",
            "INTENSIVE_EXPLOITATION"
        ]

        self.logger.info(f"Mock LLM interface initialization complete, response mode: {response_mode}")
    
    def get_response(self, prompt: str, max_retries: int = 3, retry_delay: int = 2) -> str:
        """
        Simulate LLM response

        Args:
            prompt: Input prompt text
            max_retries: Maximum retry attempts (for simulation)
            retry_delay: Retry delay (for simulation)

        Returns:
            Simulated LLM response JSON string
        """
        self.call_count += 1

        # Simulate API call delay
        time.sleep(0.1)

        if self.debug_mode:
            self.logger.info(f"Mock LLM call #{self.call_count}")
            self.logger.debug(f"Input prompt length: {len(prompt)} characters")

        # Extract individual count from prompt
        individual_count = self._extract_individual_count(prompt)

        # Generate response based on response mode
        if self.response_mode == 'intelligent':
            response = self._generate_intelligent_response(prompt, individual_count)
        elif self.response_mode == 'random':
            response = self._generate_random_response(individual_count)
        elif self.response_mode == 'fixed':
            response = self._generate_fixed_response(individual_count)
        elif self.response_mode == 'error':
            response = self._generate_error_response()
        else:
            response = self._generate_intelligent_response(prompt, individual_count)

        # Record response history
        self.response_history.append({
            'call_count': self.call_count,
            'prompt_length': len(prompt),
            'response': response,
            'timestamp': time.time()
        })

        if self.debug_mode:
            self.logger.info(f"Generated response length: {len(response)} characters")

        return response
    
    def _extract_individual_count(self, prompt: str) -> int:
        """Extract individual count from prompt"""
        # Simple heuristic method to extract individual count
        lines = prompt.split('\n')
        individual_count = 10  # Default value changed to 10

        # Try to find population size information from prompt
        import re

        # Search for population size patterns
        population_patterns = [
            r'种群大小[：:]\s*(\d+)',
            r'population\s+size[：:]\s*(\d+)',
            r'(\d+)\s*个个体',
            r'(\d+)\s*individuals'
        ]

        for pattern in population_patterns:
            matches = re.findall(pattern, prompt, re.IGNORECASE)
            if matches:
                individual_count = int(matches[0])
                break

        # If not found, try to find maximum individual ID
        if individual_count == 10:  # Still default value
            for line in lines:
                if 'individual' in line.lower() and 'id' in line.lower():
                    try:
                        # Search for patterns like "Individual 0", "Individual 1", etc.
                        matches = re.findall(r'individual[s]?\s*(\d+)', line.lower())
                        if matches:
                            max_id = max(int(m) for m in matches)
                            individual_count = max_id + 1
                            break
                    except:
                        pass

        return individual_count
    
    def _generate_intelligent_response(self, prompt: str, individual_count: int) -> str:
        """Generate intelligent response based on prompt content"""

        # Analyze landscape features in prompt
        landscape_analysis = self._analyze_prompt_landscape(prompt)

        # Generate strategy assignments
        strategy_assignments = []
        for i in range(individual_count):
            # Intelligently assign strategies based on landscape features and individual ID
            strategy_type = self._intelligent_strategy_selection(i, individual_count, landscape_analysis)

            assignment = {
                "individual_id": i,
                "strategy_type": strategy_type,
                "confidence": round(random.uniform(0.7, 0.95), 2),
                "reasoning": self._generate_reasoning(strategy_type, landscape_analysis),
                "parameters": {
                    "intensity": round(random.uniform(0.3, 0.8), 2),
                    "priority": round(random.uniform(0.4, 0.9), 2)
                }
            }
            strategy_assignments.append(assignment)

        # Calculate exploration ratio
        exploration_strategies = ["STRONG_EXPLORATION", "INTELLIGENT_EXPLORATION", "BALANCED_EXPLORATION"]
        exploration_count = sum(1 for a in strategy_assignments if a["strategy_type"] in exploration_strategies)
        exploration_ratio = exploration_count / individual_count

        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": round(exploration_ratio, 2),
                "landscape_complexity": landscape_analysis.get("complexity", 0.5),
                "key_insights": [
                    f"Strategy assignment based on landscape complexity {landscape_analysis.get('complexity', 0.5):.2f}",
                    f"Exploration strategy ratio: {exploration_ratio:.1%}",
                    "Balance exploration and exploitation to optimize convergence performance"
                ],
                "reasoning": "Intelligent strategy assignment based on current landscape features and individual states"
            }
        }

        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _analyze_prompt_landscape(self, prompt: str) -> Dict[str, Any]:
        """Analyze landscape information in prompt"""
        analysis = {
            "complexity": 0.5,
            "ruggedness": 0.5,
            "diversity": 0.5,
            "convergence": 0.5
        }

        # Simple keyword analysis
        prompt_lower = prompt.lower()

        if "high" in prompt_lower and "ruggedness" in prompt_lower:
            analysis["complexity"] = 0.8
            analysis["ruggedness"] = 0.8
        elif "low" in prompt_lower and "ruggedness" in prompt_lower:
            analysis["complexity"] = 0.3
            analysis["ruggedness"] = 0.3

        if "diversity" in prompt_lower:
            if "high" in prompt_lower:
                analysis["diversity"] = 0.8
            elif "low" in prompt_lower:
                analysis["diversity"] = 0.3

        return analysis
    
    def _intelligent_strategy_selection(self, individual_id: int, total_count: int, landscape: Dict) -> str:
        """Intelligent strategy selection logic"""

        # Strategy tendency based on individual position
        position_ratio = individual_id / max(total_count - 1, 1)

        # Adjust based on landscape complexity
        complexity = landscape.get("complexity", 0.5)

        if complexity > 0.7:  # High complexity landscape
            if position_ratio < 0.7:
                return random.choice(["STRONG_EXPLORATION", "INTELLIGENT_EXPLORATION"])
            else:
                return "BALANCED_EXPLORATION"
        elif complexity < 0.3:  # Low complexity landscape
            if position_ratio < 0.3:
                return "BALANCED_EXPLORATION"
            else:
                return random.choice(["MODERATE_EXPLOITATION", "INTENSIVE_EXPLOITATION"])
        else:  # Medium complexity
            if position_ratio < 0.4:
                return "INTELLIGENT_EXPLORATION"
            elif position_ratio < 0.7:
                return "BALANCED_EXPLORATION"
            else:
                return "MODERATE_EXPLOITATION"
    
    def _generate_reasoning(self, strategy_type: str, landscape: Dict) -> str:
        """Generate strategy selection reasoning"""
        complexity = landscape.get("complexity", 0.5)

        reasoning_templates = {
            "STRONG_EXPLORATION": f"Landscape complexity {complexity:.2f}, requires strong exploration to discover new regions",
            "INTELLIGENT_EXPLORATION": f"Intelligent exploration strategy based on landscape features",
            "BALANCED_EXPLORATION": f"Balance exploration and exploitation, adapt to medium complexity landscape",
            "MODERATE_EXPLOITATION": f"Landscape relatively smooth, suitable for moderate exploitation strategy",
            "INTENSIVE_EXPLOITATION": f"Low complexity landscape, concentrate on exploiting current advantageous regions"
        }

        return reasoning_templates.get(strategy_type, "Strategy selection based on current state")
    
    def _generate_random_response(self, individual_count: int) -> str:
        """Generate random response"""
        strategy_assignments = []
        for i in range(individual_count):
            assignment = {
                "individual_id": i,
                "strategy_type": random.choice(self.strategy_types),
                "confidence": round(random.uniform(0.5, 0.9), 2),
                "reasoning": "Random strategy assignment",
                "parameters": {
                    "intensity": round(random.uniform(0.2, 0.8), 2),
                    "priority": round(random.uniform(0.3, 0.9), 2)
                }
            }
            strategy_assignments.append(assignment)

        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": round(random.uniform(0.4, 0.8), 2),
                "key_insights": ["Random strategy assignment for testing"],
                "reasoning": "Random mode response"
            }
        }

        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _generate_fixed_response(self, individual_count: int) -> str:
        """Generate fixed response"""
        strategy_assignments = []
        for i in range(individual_count):
            # Fixed mode: 60% exploration, 40% exploitation
            if i < individual_count * 0.6:
                strategy_type = "INTELLIGENT_EXPLORATION"
            else:
                strategy_type = "MODERATE_EXPLOITATION"

            assignment = {
                "individual_id": i,
                "strategy_type": strategy_type,
                "confidence": 0.8,
                "reasoning": "Fixed strategy assignment mode",
                "parameters": {
                    "intensity": 0.6,
                    "priority": 0.7
                }
            }
            strategy_assignments.append(assignment)

        response = {
            "strategy_assignments": strategy_assignments,
            "global_analysis": {
                "exploration_ratio": 0.6,
                "key_insights": ["Fixed 60% exploration, 40% exploitation strategy assignment"],
                "reasoning": "Fixed mode response"
            }
        }

        return json.dumps(response, indent=2, ensure_ascii=False)
    
    def _generate_error_response(self) -> str:
        """Generate error response (for testing error handling)"""
        return "ERROR: Mock LLM call failed"

    def get_statistics(self) -> Dict[str, Any]:
        """Get call statistics"""
        return {
            "total_calls": self.call_count,
            "response_mode": self.response_mode,
            "average_response_time": 0.1,  # Simulated value
            "success_rate": 1.0 if self.response_mode != 'error' else 0.0
        }
