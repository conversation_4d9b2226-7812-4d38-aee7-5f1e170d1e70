# -*- coding: utf-8 -*-
"""
多专家协作进化算法框架 - 主程序

模块化重构后的主程序，只包含main函数和必要的导入。
所有专家类已分离到独立模块中。
"""

import argparse
import copy
import logging
import os
import sys
import time

# 导入配置和工具模块
from config.config import ALGORITHM_CONFIG, GLOBAL_INPUT_PATH, GLOBAL_OUTPUT_PATH
from config.landscape_config import LandscapeConfig
from experts.management.collaboration_manager import ExpertCollaborationManager
from api.api_general import InterfaceAPI
from core.data.loadinstance import load_all_instances
from config.log_config import setup_logging, get_instance_log_file
from core.data.initpop import INIT
from utils import utils
from utils.jit_warmup import warmup_jit_functions

# 导入评估统计系统
from evaluation.integration import setup_evaluation_system
from evaluation.config import get_global_config_manager
from evaluation import AlgorithmPhase
from evaluation.context import EvaluationContext
from evaluation.monitor import EvaluationReporter

# 获取当前目录和src根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
src_root = current_dir  # src目录就是当前目录

# 设置主日志记录器
main_logger = logging.getLogger(__name__)


def main():
    """多专家系统主函数"""
    # 导入并调用JIT预热模块，确保在处理实际数据前完成JIT编译
    # from utils.jit_warmup import warmup_jit_functions
    # warmup_jit_functions()

    # 初始化评估统计系统
    print(">> 初始化EoH-TSP-Solver系统...")
    counter = None
    integrator = None
    evaluation_enabled = False

    try:
        config_manager = get_global_config_manager()
        config = config_manager.config
        evaluation_enabled = config.enabled

        # 统一调用setup_evaluation_system，它会根据配置决定是否创建计数器
        print(">> 正在初始化评估统计系统...")
        counter, integrator = setup_evaluation_system(config)

        if config.enabled and counter is not None and integrator is not None:
            print(f">> 评估统计系统已启用")
            print(f"   详细记录: {'启用' if config.detailed_logging else '禁用'}")
            print(f"   最大记录数: {config.max_records}")
            print(f"   集成模块数: {integrator.get_integration_status()['integrated_modules']}")
        else:
            print(">> 评估统计系统已禁用 - 将运行核心TSP算法")
            print("   注意: 不会收集评估次数统计信息")
            counter = None
            integrator = None
            evaluation_enabled = False
    except Exception as e:
        print(f">> 评估统计系统初始化失败: {e}")
        print(">> 继续运行核心TSP算法（无评估统计）")
        counter = None
        integrator = None
        evaluation_enabled = False
    
    parser = argparse.ArgumentParser(description="多专家协作进化算法框架")
    parser.add_argument('--func_begin', type=int, required=False, default=24, help='起始索引，默认为0')
    parser.add_argument('--func_end', type=int, required=False, default=24, help='结束索引，默认为0')
    parser.add_argument('--iter_num', type=int, help='迭代次数', default=5)
    parser.add_argument('--pop_size', type=int, help='种群规模', default=10)
    args = parser.parse_args()

    # 定义实例列表并处理空值
    func_name = [
        "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
        "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
        "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39", 
        "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite12_60", "composite13_66",
        "eil51", "berlin52", "st70", "pr76", "kroA100", "lin105"
    ]
    func_name = [name for name in func_name if name]

    # 设置func_end默认值
    if args.func_end == 0 or args.func_end >= len(func_name):
        args.func_end = args.func_begin

    # 验证参数有效性
    if args.func_begin < 0 or args.func_end >= len(func_name) or args.func_begin > args.func_end:
        print(f"错误：参数范围无效。有效范围: 0-{len(func_name)-1}，当前值: {args.func_begin}-{args.func_end}")
        sys.exit(1)
    
    # 设置日志系统
    # 修改日志保存路径到src/results/logs文件夹
    log_dir = os.path.join(src_root, "results", "logs")
    os.makedirs(log_dir, exist_ok=True)
    setup_logging(log_dir=log_dir, log_file="moe_app.log")

    # 打印路径信息以便调试
    main_logger.info(f"src根目录: {src_root}")
    main_logger.info(f"输入路径: {GLOBAL_INPUT_PATH}")
    main_logger.info(f"输出路径: {GLOBAL_OUTPUT_PATH}")
    
    # 检查输入目录是否存在
    if not os.path.exists(GLOBAL_INPUT_PATH):
        print(f"错误: 输入目录不存在: {GLOBAL_INPUT_PATH}")
        return
    
    # 确保目录存在
    os.makedirs(GLOBAL_INPUT_PATH, exist_ok=True)
    os.makedirs(GLOBAL_OUTPUT_PATH, exist_ok=True)
    
    # 从配置文件获取算法参数
    pop_size = args.pop_size if args.pop_size else ALGORITHM_CONFIG.get("pop_size", 20)
    evo_num = args.iter_num if args.iter_num else ALGORITHM_CONFIG.get("evo_num", 10)
    
    # 加载实例 - 使用封装的函数直接返回instances_selected
    instances_selected = load_all_instances(func_name, GLOBAL_INPUT_PATH, args.func_begin, args.func_end, GLOBAL_OUTPUT_PATH)

    if len(instances_selected) == 0:
        print("警告: 没有成功加载任何实例")
        return
    
    # 加载可视化配置
    landscape_config = LandscapeConfig()
    config = {
        'landscape_config': {
            'visualization_config': landscape_config.get_visualization_config(),
            'algorithmic_config': landscape_config.get_algorithmic_config(),
            'performance_config': landscape_config.get_performance_config()
        },
        # 添加LLM策略选择配置
        'strategy_config': {
            'enable_llm_reasoning': False,  # 启用LLM策略选择
            'fallback_to_algorithmic': True,  # 启用算法回退
            'max_llm_retries': 3,
            'state_analyzer': {
                'history_window': 15,
                'improvement_threshold': 1e-5,
                'diversity_method': 'hamming'
            },
            'response_parser': {
                'strict_validation': False,
                'auto_repair': True,
                'default_confidence': 0.6,
                'default_priority': 0.5
            }
        }
    }

    # API接口设置
    debug_mode = False

    # 检查是否启用LLM策略选择
    strategy_config = config.get('strategy_config', {})
    enable_llm = strategy_config.get('enable_llm_reasoning', False)

    if enable_llm:
        # 检查是否使用模拟模式
        if os.getenv('LLM_MOCK_MODE') == 'true':
            main_logger.info("使用模拟LLM接口进行测试")
            from api.mock_llm_interface import MockLLMInterface
            interface_llm = MockLLMInterface(response_mode='intelligent', debug_mode=debug_mode)
        else:
            # 使用真实LLM接口
            main_logger.info("使用真实LLM接口")
            interface_llm = InterfaceAPI(api_type="gemini", debug_mode=debug_mode)
            # interface_llm = InterfaceAPI(api_type="deepseek", debug_mode=debug_mode)
    else:
        # 不启用LLM时使用默认接口
        interface_llm = InterfaceAPI(api_type="gemini", debug_mode=debug_mode)

    # 确保可视化输出目录存在
    viz_config = config['landscape_config']['visualization_config']
    viz_output_dir = viz_config.get('save_dir', 'visualization_output')
    if not os.path.isabs(viz_output_dir):
        viz_output_dir = os.path.join(src_root, viz_output_dir)
    os.makedirs(viz_output_dir, exist_ok=True)

    # 更新配置中的输出目录为绝对路径
    config['landscape_config']['visualization_config']['save_dir'] = viz_output_dir

    main_logger.info(f"可视化配置已加载:")
    main_logger.info(f"  - 启用状态: {viz_config.get('enabled', False)}")
    main_logger.info(f"  - 输出目录: {viz_output_dir}")

    # 初始化专家协作管理器（传递配置）
    collaboration_manager = ExpertCollaborationManager(interface_llm, config)
    
    # 主循环，对每个实例进行处理
    for iter_idx in range(len(instances_selected)):
        instance = instances_selected[iter_idx]
        main_logger.info(f"开始处理实例: {instance['func_name']}")

        # 记录实例开始时间
        instance_start_time = time.time()

        # 初始化种群
        print(f">> 正在初始化种群 (规模: {pop_size})...")
        if counter is not None:
            # 使用评估统计系统
            with EvaluationContext(AlgorithmPhase.INITIALIZATION, counter=counter) as init_ctx:
                populations = INIT.mixed_init(instance['distance_matrix'], pop_size)
                populations = INIT.calculate_population_costs(populations, instance['distance_matrix'])
                cur_best_cost = min(populations, key=lambda x: x["cur_cost"])["cur_cost"]
                print(f"当前最佳适应度：{cur_best_cost}")
                print(f">> 初始化阶段评估次数: {init_ctx.evaluation_count}")
                main_logger.info(f"初始化种群完成，当前最佳适应度: {cur_best_cost}, 评估次数: {init_ctx.evaluation_count}")
        else:
            # 不使用评估统计系统
            populations = INIT.mixed_init(instance['distance_matrix'], pop_size)
            populations = INIT.calculate_population_costs(populations, instance['distance_matrix'])
            cur_best_cost = min(populations, key=lambda x: x["cur_cost"])["cur_cost"]
            print(f"当前最佳适应度：{cur_best_cost}")
            main_logger.info(f"初始化种群完成，当前最佳适应度: {cur_best_cost}")
        
        # 为当前实例创建专用的日志文件处理器
        # 确保使用src/results/logs文件夹保存实例日志
        instance_log_dir = os.path.join(src_root, "results", "logs")
        os.makedirs(instance_log_dir, exist_ok=True)
        instance_log_file = get_instance_log_file(instance['func_name'], instance_log_dir)
        file_handler = logging.FileHandler(instance_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        root_logger = logging.getLogger('')
        root_logger.addHandler(file_handler)
        
        # 存储各代进化个体和精英解
        res_populations = []  # 存储精英解
        strategy_feedback = None

        # 每个实例进化evo_num代
        for evo_iter in range(evo_num):
            main_logger.info(f"{instance['func_name']} 开始进化第 {evo_iter+1} 代")
            print(f"iter: {evo_iter}")

            # 1. 分析阶段
            if counter is not None:
                # 使用评估统计系统
                with EvaluationContext(AlgorithmPhase.LANDSCAPE_ANALYSIS, evo_iter, counter) as analysis_ctx:
                    landscape_report, old_stats_report = collaboration_manager.run_analysis_phase(
                        populations=populations,
                        res_populations=res_populations,
                        coordinates=instance["coordinate"],
                        distance_matrix=instance["distance_matrix"],
                        iteration=evo_iter,
                        total_iterations=evo_num,
                        instance_name=instance["func_name"]
                    )
                    if evo_iter % 5 == 0:  # 每5代显示一次评估统计
                        print(f">> 分析阶段评估次数: {analysis_ctx.evaluation_count}")
            else:
                # 不使用评估统计系统
                landscape_report, old_stats_report = collaboration_manager.run_analysis_phase(
                    populations=populations,
                    res_populations=res_populations,
                    coordinates=instance["coordinate"],
                    distance_matrix=instance["distance_matrix"],
                    iteration=evo_iter,
                    total_iterations=evo_num,
                    instance_name=instance["func_name"]
                )
            # 记录完整的景观分析报告
            main_logger.info(f"景观分析完整报告: {landscape_report}")

            # 2. 策略分配阶段
            if counter is not None:
                # 使用评估统计系统
                with EvaluationContext(AlgorithmPhase.STRATEGY_SELECTION, evo_iter, counter):
                    strategy_result = collaboration_manager.run_strategy_phase(
                        landscape_report=landscape_report,
                        populations=populations,
                        iteration=evo_iter,
                        strategy_feedback=strategy_feedback
                    )
                    strategy_selection, strategy_response = strategy_result
            else:
                # 不使用评估统计系统
                strategy_result = collaboration_manager.run_strategy_phase(
                    landscape_report=landscape_report,
                    populations=populations,
                    iteration=evo_iter,
                    strategy_feedback=strategy_feedback
                )
                strategy_selection, strategy_response = strategy_result
            # 记录完整的策略分配报告
            main_logger.info(f"策略分配: {strategy_selection}")
            main_logger.info(f"策略分配完整报告: {strategy_response}")

            # 保存当前种群副本用于后续评估
            copy.deepcopy(populations)

            # 3. 进化阶段
            if counter is not None:
                # 使用评估统计系统
                with EvaluationContext(AlgorithmPhase.EVOLUTION, evo_iter, counter) as evolution_ctx:
                    new_populations = collaboration_manager.run_evolution_phase(
                        populations=populations,
                        strategies=strategy_selection,
                        landscape_report=landscape_report,
                        distance_matrix=instance["distance_matrix"],
                        res_populations=res_populations,  # 传递精英解集合
                        iteration=evo_iter,              # 传递当前迭代次数
                        total_iterations=evo_num         # 传递总迭代次数
                    )
                    if evo_iter % 5 == 0:  # 每5代显示一次评估统计
                        print(f">> 进化阶段评估次数: {evolution_ctx.evaluation_count}")
            else:
                # 不使用评估统计系统
                new_populations = collaboration_manager.run_evolution_phase(
                    populations=populations,
                    strategies=strategy_selection,
                    landscape_report=landscape_report,
                    distance_matrix=instance["distance_matrix"],
                    res_populations=res_populations,  # 传递精英解集合
                    iteration=evo_iter,              # 传递当前迭代次数
                    total_iterations=evo_num         # 传递总迭代次数
                )

            # 更新种群
            populations = new_populations
            res_populations.sort(key=lambda x: x["cur_cost"])

            # 4. 评估阶段
            if counter is not None:
                # 使用评估统计系统
                with EvaluationContext(AlgorithmPhase.ASSESSMENT, evo_iter, counter):
                    # 为新种群计算统计数据
                    stats_expert = collaboration_manager.experts["stats"]
                    new_stats_analysis = stats_expert.analyze(populations)
                    new_stats_report = stats_expert.generate_report(new_stats_analysis, instance["coordinate"], instance["distance_matrix"], instance["func_name"])

                    assessment_report = collaboration_manager.run_assessment_phase(
                        old_stats_report, new_stats_report, strategy_selection, evo_iter, evo_num,
                        old_res_populations=res_populations, new_res_populations=res_populations
                    )
            else:
                # 不使用评估统计系统
                stats_expert = collaboration_manager.experts["stats"]
                new_stats_analysis = stats_expert.analyze(populations)
                new_stats_report = stats_expert.generate_report(new_stats_analysis, instance["coordinate"], instance["distance_matrix"], instance["func_name"])

                assessment_report = collaboration_manager.run_assessment_phase(
                    old_stats_report, new_stats_report, strategy_selection, evo_iter, evo_num,
                    old_res_populations=res_populations, new_res_populations=res_populations
                )

            # 更新下一次迭代的输入
            strategy_feedback = assessment_report
            populations = new_populations
            res_populations = res_populations

            main_logger.info(f"--- Finished Evolution Iteration {evo_iter+1} ---")

        # 保存最终结果，使用封装的保存函数
        output_base_dir = src_root
        utils.save_optimization_results(
            res_populations=res_populations,
            instance_name=instance['func_name'],
            output_base_dir=output_base_dir,
            logger=main_logger
        )

        # 生成实例执行报告
        instance_end_time = time.time()
        instance_duration = instance_end_time - instance_start_time
        best_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"] if res_populations else "N/A"

        if counter is not None:
            # 生成详细的评估统计报告
            print(f"\n>> 实例 {instance['func_name']} 评估统计报告:")
            print("=" * 60)

            # 获取统计信息
            final_stats = counter.get_statistics()

            print(f">> 算法执行结果:")
            print(f"   实例名称: {instance['func_name']}")
            print(f"   最佳成本: {best_cost}")
            print(f"   总评估次数: {final_stats.total_count:,}")
            print(f"   实例运行时间: {instance_duration:.2f} 秒")
            print(f"   平均评估速率: {final_stats.total_count/instance_duration:.1f} 次/秒")
            print(f"   内存使用: {counter.get_memory_usage_mb():.2f} MB")
        else:
            # 生成简化的执行报告
            print(f"\n>> 实例 {instance['func_name']} 执行完成:")
            print("=" * 60)
            print(f">> 算法执行结果:")
            print(f"   实例名称: {instance['func_name']}")
            print(f"   最佳成本: {best_cost}")
            print(f"   实例运行时间: {instance_duration:.2f} 秒")
            print(f"   注意: 评估统计系统已禁用，无评估次数统计")

        # 仅在启用评估统计时显示详细统计信息
        if counter is not None:
            print(f"\n>> 评估类型分布:")
            for eval_type, count in final_stats.by_type.items():
                if count > 0:
                    percentage = count / final_stats.total_count * 100
                    print(f"   {eval_type.value}: {count:,} ({percentage:.1f}%)")

            print(f"\n>> 算法阶段分布:")
            for phase, count in final_stats.by_phase.items():
                if count > 0:
                    percentage = count / final_stats.total_count * 100
                    print(f"   {phase.value}: {count:,} ({percentage:.1f}%)")

            # 生成详细报告文件
            try:
                reporter = EvaluationReporter(counter)
                report_filename = f"evaluation_report_{instance['func_name']}.json"
                success = reporter.export_report(report_filename, "json", detailed=True)
                if success:
                    print(f"\n>> 详细报告已保存: {report_filename}")

                # 记录到日志
                main_logger.info(f"评估统计 - 总次数: {final_stats.total_count}, 运行时间: {instance_duration:.2f}s, 最佳成本: {best_cost}")
            except Exception as e:
                print(f">> 报告生成失败: {e}")
        else:
            # 记录到日志（无评估统计）
            main_logger.info(f"实例执行完成 - 运行时间: {instance_duration:.2f}s, 最佳成本: {best_cost}")

        print("=" * 60)

        main_logger.info(f"实例 {instance['func_name']} 处理完成")

        # 移除实例专用的日志处理器
        root_logger.removeHandler(file_handler)
        file_handler.close()

    # 程序结束时的总结
    if evaluation_enabled and counter is not None and integrator is not None:
        # 显示评估统计系统总结
        print(f"\n>> EoH-TSP-Solver 评估统计系统总结:")
        print("=" * 60)

        final_stats = counter.get_statistics()
        total_runtime = final_stats.get_elapsed_time()

        print(f">> 处理实例数: {len(instances_selected)}")
        print(f">> 总评估次数: {final_stats.total_count:,}")
        print(f">> 总运行时间: {total_runtime:.2f} 秒")
        print(f">> 平均评估速率: {final_stats.get_evaluations_per_second():.1f} 次/秒")
        print(f">> 系统内存使用: {counter.get_memory_usage_mb():.2f} MB")

        # 清理集成
        try:
            cleanup_result = integrator.cleanup_integration()
            print(f">> 清理完成: {cleanup_result['cleaned_modules']}/{cleanup_result['total_modules']} 模块")
        except Exception as e:
            print(f">> 清理失败: {e}")

        print("=" * 60)
        print(">> 评估统计系统运行完成！")
    else:
        # 显示简化的程序总结
        print(f"\n>> EoH-TSP-Solver 运行完成:")
        print("=" * 60)
        print(f">> 处理实例数: {len(instances_selected)}")
        print(f">> 注意: 评估统计系统已禁用")
        print("=" * 60)
        print(">> 程序运行完成！")

    # 确保在程序结束时清理全局计数器
    if not evaluation_enabled:
        try:
            from evaluation import clear_global_counter
            clear_global_counter()
        except Exception:
            pass


if __name__ == "__main__":
    main()
