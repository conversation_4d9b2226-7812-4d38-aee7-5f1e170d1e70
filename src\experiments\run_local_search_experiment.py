#!/usr/bin/env python3
"""
简化版局部搜索优化效果实验运行脚本
直接运行对比实验，无需复杂的依赖
"""

import os
import sys
import time
import numpy as np
import random
from collections import defaultdict

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def generate_test_instance(size, seed=42):
    """生成测试实例"""
    np.random.seed(seed)
    random.seed(seed)
    
    # 生成随机坐标
    coordinates = np.random.rand(size, 2) * 1000
    
    # 计算距离矩阵
    distance_matrix = np.zeros((size, size))
    for i in range(size):
        for j in range(size):
            if i != j:
                distance_matrix[i][j] = np.sqrt(
                    (coordinates[i][0] - coordinates[j][0])**2 + 
                    (coordinates[i][1] - coordinates[j][1])**2
                )
    
    # 生成初始解
    initial_tour = np.arange(size, dtype=np.int64)
    np.random.shuffle(initial_tour[1:])  # 保持起点为0
    
    return distance_matrix, initial_tour

def tour_cost_simple(distance_matrix, tour):
    """简单的路径成本计算"""
    cost = 0
    n = len(tour)
    for i in range(n):
        cost += distance_matrix[tour[i], tour[(i + 1) % n]]
    return cost

def two_opt_simple(tour, distance_matrix):
    """简化的2-opt算法"""
    n = len(tour)
    best_delta = 0
    best_i, best_j = -1, -1
    
    for i in range(n - 1):
        for j in range(i + 2, n):
            if j == n - 1 and i == 0:
                continue
            
            # 计算2-opt交换的成本变化
            current_cost = (distance_matrix[tour[i], tour[i + 1]] + 
                          distance_matrix[tour[j], tour[(j + 1) % n]])
            new_cost = (distance_matrix[tour[i], tour[j]] + 
                       distance_matrix[tour[i + 1], tour[(j + 1) % n]])
            
            delta = new_cost - current_cost
            if delta < best_delta:
                best_delta = delta
                best_i, best_j = i, j
    
    if best_delta < 0:
        # 执行2-opt交换
        new_tour = tour.copy()
        new_tour[best_i + 1:best_j + 1] = new_tour[best_i + 1:best_j + 1][::-1]
        return best_delta, new_tour
    
    return 0, tour

def relocate_simple(tour, distance_matrix):
    """简化的重定位算法"""
    n = len(tour)
    best_delta = 0
    best_move = None
    
    for i in range(n):
        for j in range(n):
            if i == j or abs(i - j) == 1 or (i == 0 and j == n - 1) or (i == n - 1 and j == 0):
                continue
            
            # 计算重定位的成本变化
            current_cost = (distance_matrix[tour[(i - 1) % n], tour[i]] + 
                          distance_matrix[tour[i], tour[(i + 1) % n]] +
                          distance_matrix[tour[(j - 1) % n], tour[j]])
            
            new_cost = (distance_matrix[tour[(i - 1) % n], tour[(i + 1) % n]] +
                       distance_matrix[tour[(j - 1) % n], tour[i]] +
                       distance_matrix[tour[i], tour[j]])
            
            delta = new_cost - current_cost
            if delta < best_delta:
                best_delta = delta
                best_move = (i, j)
    
    if best_delta < 0 and best_move:
        # 执行重定位
        new_tour = tour.copy().tolist()
        i, j = best_move
        node = new_tour.pop(i)
        if j > i:
            j -= 1
        new_tour.insert(j, node)
        return best_delta, np.array(new_tour)
    
    return 0, tour

def original_local_search(distance_matrix, initial_tour, time_limit=5.0):
    """原始版本的局部搜索（包含性能问题）"""
    start_time = time.time()
    current_tour = initial_tour.copy()
    current_cost = tour_cost_simple(distance_matrix, current_tour)
    
    stats = {
        'iterations': 0,
        'improvements': 0,
        'time_calls': 0,
        'array_comparisons': 0,
        'cost_recalculations': 0
    }
    
    improved = True
    end_time = start_time + time_limit
    
    while improved and time.time() < end_time:
        improved = False
        stats['iterations'] += 1
        
        # 原始版本：重复的时间记录
        two_opt_start = time.time()  # 时间调用1
        stats['time_calls'] += 1
        
        delta, new_tour = two_opt_simple(current_tour, distance_matrix)
        
        # 原始版本：包含delta==0判断和数组比较
        if delta < 0 or (delta == 0 and not np.array_equal(new_tour, current_tour)):
            stats['array_comparisons'] += 1
            improved = True
            # 原始版本：重新计算完整成本
            current_cost = tour_cost_simple(distance_matrix, new_tour)
            stats['cost_recalculations'] += 1
            current_tour = new_tour
            stats['improvements'] += 1
        
        # 更多时间记录开销
        _ = time.time() - two_opt_start  # 时间调用2
        stats['time_calls'] += 1
        
        # Relocate算子 - 同样的问题
        relocate_start = time.time()  # 时间调用3
        stats['time_calls'] += 1
        
        delta, new_tour = relocate_simple(current_tour, distance_matrix)
        
        if delta < 0 or (delta == 0 and not np.array_equal(new_tour, current_tour)):
            stats['array_comparisons'] += 1
            improved = True
            current_cost = tour_cost_simple(distance_matrix, new_tour)
            stats['cost_recalculations'] += 1
            current_tour = new_tour
            stats['improvements'] += 1
        
        _ = time.time() - relocate_start  # 时间调用4
        stats['time_calls'] += 1
        
        # 额外的无用时间调用
        _ = time.time()  # 时间调用5
        stats['time_calls'] += 1
    
    total_time = time.time() - start_time
    final_cost = tour_cost_simple(distance_matrix, current_tour)
    
    return {
        'final_tour': current_tour,
        'final_cost': final_cost,
        'total_time': total_time,
        'stats': stats
    }

def optimized_local_search(distance_matrix, initial_tour, time_limit=5.0):
    """优化版本的局部搜索"""
    start_time = time.time()
    current_tour = initial_tour.copy()
    current_cost = tour_cost_simple(distance_matrix, current_tour)
    
    stats = {
        'iterations': 0,
        'improvements': 0,
        'time_calls': 0,
        'array_comparisons': 0,
        'cost_recalculations': 0,
        'early_stops': 0
    }
    
    stagnation_count = 0
    stagnation_limit = 10
    end_time = start_time + time_limit
    
    while stats['iterations'] < 100 and time.time() < end_time:
        stats['iterations'] += 1
        
        best_delta = 0
        best_tour = None
        loop_improved = False
        
        # 优化版本：减少时间记录，只在必要时记录
        # 不记录每个算子的单独时间，减少开销
        
        # Two-opt算子
        delta, new_tour = two_opt_simple(current_tour, distance_matrix)
        if delta < best_delta:
            best_delta = delta
            best_tour = new_tour
            loop_improved = True
        
        # Relocate算子
        delta, new_tour = relocate_simple(current_tour, distance_matrix)
        if delta < best_delta:
            best_delta = delta
            best_tour = new_tour
            loop_improved = True
        
        # 应用最佳改进
        if loop_improved and best_delta < 0:
            current_cost += best_delta  # 优化版本：增量更新成本
            current_tour = best_tour
            stats['improvements'] += 1
            stagnation_count = 0
        else:
            stagnation_count += 1
        
        # 智能早停机制
        if stagnation_count >= stagnation_limit:
            stats['early_stops'] += 1
            break
    
    total_time = time.time() - start_time
    final_cost = tour_cost_simple(distance_matrix, current_tour)
    
    return {
        'final_tour': current_tour,
        'final_cost': final_cost,
        'total_time': total_time,
        'stats': stats
    }

def run_comparison_experiment():
    """运行对比实验"""
    print("=" * 80)
    print("局部搜索核心循环优化效果对比实验")
    print("=" * 80)
    
    # 测试实例配置
    test_instances = [
        {'name': 'small_50', 'size': 50},
        {'name': 'medium_100', 'size': 100},
        {'name': 'large_150', 'size': 150}
    ]
    
    runs_per_instance = 5
    results = []
    
    for instance in test_instances:
        print(f"\n测试实例: {instance['name']} (规模: {instance['size']})")
        
        # 生成测试数据
        distance_matrix, initial_tour = generate_test_instance(instance['size'])
        initial_cost = tour_cost_simple(distance_matrix, initial_tour)
        
        print(f"  初始成本: {initial_cost:.2f}")
        
        # 对每个算法运行多次
        for algorithm in ['original', 'optimized']:
            print(f"    算法: {algorithm}")
            
            algorithm_results = []
            for run in range(runs_per_instance):
                if algorithm == 'original':
                    result = original_local_search(distance_matrix, initial_tour)
                else:
                    result = optimized_local_search(distance_matrix, initial_tour)
                
                algorithm_results.append(result)
                improvement = (initial_cost - result['final_cost']) / initial_cost * 100
                print(f"      运行 {run+1}: 时间={result['total_time']:.3f}s, "
                      f"改进={improvement:.2f}%, 迭代={result['stats']['iterations']}")
            
            # 计算平均结果
            avg_time = np.mean([r['total_time'] for r in algorithm_results])
            avg_improvement = np.mean([(initial_cost - r['final_cost']) / initial_cost * 100 
                                     for r in algorithm_results])
            avg_iterations = np.mean([r['stats']['iterations'] for r in algorithm_results])
            avg_improvements = np.mean([r['stats']['improvements'] for r in algorithm_results])
            
            results.append({
                'instance': instance['name'],
                'size': instance['size'],
                'algorithm': algorithm,
                'avg_time': avg_time,
                'avg_improvement': avg_improvement,
                'avg_iterations': avg_iterations,
                'avg_improvements': avg_improvements,
                'results': algorithm_results
            })
            
            print(f"      平均: 时间={avg_time:.3f}s, 改进={avg_improvement:.2f}%, "
                  f"迭代={avg_iterations:.1f}")
    
    return results

def analyze_results(results):
    """分析实验结果"""
    print("\n" + "=" * 80)
    print("实验结果分析")
    print("=" * 80)
    
    # 按算法类型分组
    original_results = [r for r in results if r['algorithm'] == 'original']
    optimized_results = [r for r in results if r['algorithm'] == 'optimized']
    
    # 计算整体改进
    original_avg_time = np.mean([r['avg_time'] for r in original_results])
    optimized_avg_time = np.mean([r['avg_time'] for r in optimized_results])
    time_improvement = (original_avg_time - optimized_avg_time) / original_avg_time * 100
    
    original_avg_improvement = np.mean([r['avg_improvement'] for r in original_results])
    optimized_avg_improvement = np.mean([r['avg_improvement'] for r in optimized_results])
    quality_improvement = optimized_avg_improvement - original_avg_improvement
    
    print(f"\n整体性能对比:")
    print(f"  原始算法平均执行时间: {original_avg_time:.3f}s")
    print(f"  优化算法平均执行时间: {optimized_avg_time:.3f}s")
    print(f"  执行时间改进: {time_improvement:.2f}%")
    print(f"  解质量改进: {quality_improvement:.2f}%")
    
    # 按规模分析
    print(f"\n按问题规模分析:")
    for size in [50, 100, 150]:
        original_size = [r for r in original_results if r['size'] == size]
        optimized_size = [r for r in optimized_results if r['size'] == size]
        
        if original_size and optimized_size:
            orig_time = original_size[0]['avg_time']
            opt_time = optimized_size[0]['avg_time']
            improvement = (orig_time - opt_time) / orig_time * 100
            
            print(f"  规模 {size}: 时间改进 {improvement:.2f}%")
    
    # 验证预期效果
    print(f"\n预期效果验证:")
    if time_improvement >= 30:
        print("✓ 执行时间改进达到预期 (≥30%)")
    else:
        print(f"⚠ 执行时间改进未达预期 ({time_improvement:.2f}% < 30%)")
    
    return {
        'time_improvement': time_improvement,
        'quality_improvement': quality_improvement
    }

def main():
    """主函数"""
    print("开始局部搜索优化效果实验...")
    
    start_time = time.time()
    results = run_comparison_experiment()
    analysis = analyze_results(results)
    end_time = time.time()
    
    print(f"\n实验总耗时: {end_time - start_time:.2f} 秒")
    print(f"实验完成!")
    
    return results, analysis

if __name__ == "__main__":
    main()
