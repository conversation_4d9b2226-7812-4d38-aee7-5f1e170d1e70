#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validation script for prompt conversion from Chinese to English
Tests that all converted prompts maintain the same functionality and output formats
"""

import sys
import os
import json

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_enhanced_strategy_prompt_generation():
    """Test that enhanced strategy prompt generation works with English prompts"""
    try:
        from experts.prompts.enhanced_strategy_prompts import generate_individual_strategy_prompt
        
        # Create test data
        individual_features = [
            {
                'individual_id': 0,
                'fitness_value': 95.8,
                'fitness_rank': 0,
                'fitness_percentile': 0.0,
                'stagnation_duration': 0,
                'stagnation_level': 'none',
                'diversity_contribution': 0.8,
                'distance_to_best': 0.0,
                'recent_improvements': 2,
                'preferred_strategies': ['moderate_exploitation']
            },
            {
                'individual_id': 1,
                'fitness_value': 120.3,
                'fitness_rank': 4,
                'fitness_percentile': 0.8,
                'stagnation_duration': 5,
                'stagnation_level': 'moderate',
                'diversity_contribution': 0.3,
                'distance_to_best': 24.5,
                'recent_improvements': 0,
                'preferred_strategies': []
            }
        ]
        
        landscape_context = {
            'global_ruggedness': 0.65,
            'modality': 'multi_modal',
            'deceptiveness': 'medium',
            'gradient_strength': 0.72,
            'population_diversity': 0.45,
            'convergence_trend': 0.38,
            'evolution_phase': 'exploration',
            'difficult_regions': [],
            'opportunity_regions': []
        }
        
        # Generate prompt
        prompt = generate_individual_strategy_prompt(
            individual_features, landscape_context, iteration=10
        )
        
        # Validate prompt content
        assert isinstance(prompt, str), "Prompt should be a string"
        assert len(prompt) > 100, "Prompt should be substantial"
        
        # Check for English content (should not contain Chinese characters)
        chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in prompt)
        if chinese_chars:
            print("WARNING: Prompt still contains Chinese characters")
            return False
        
        # Check for key English terms
        english_terms = [
            'TSP Optimization Strategy Selection Expert',
            'Individual 0',
            'Individual 1', 
            'Fitness:',
            'Strategy Type',
            'exploration',
            'exploitation'
        ]
        
        missing_terms = []
        for term in english_terms:
            if term not in prompt:
                missing_terms.append(term)
        
        if missing_terms:
            print(f"WARNING: Missing expected English terms: {missing_terms}")
            return False
        
        print("✓ Enhanced strategy prompt generation test passed")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced strategy prompt generation test failed: {e}")
        return False

def test_experts_prompt_templates():
    """Test that experts prompt templates contain English content"""
    try:
        from experts.prompts.experts_prompt import LANDSCAPE_PROMPT, EXPLORATION_PROMPT, EXPLOITATION_PROMPT

        # Test landscape prompt template
        landscape_template = LANDSCAPE_PROMPT
        assert isinstance(landscape_template, str), "Landscape prompt should be a string"

        # Check for English content (should not contain Chinese characters)
        chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in landscape_template)
        if chinese_chars:
            print("WARNING: Landscape prompt template still contains Chinese characters")
            return False

        # Check for key English terms
        english_terms = [
            'Landscape Analysis Expert',
            'Instance',
            'Evolution snapshot',
            'population_size',
            'cost_stats'
        ]

        missing_terms = []
        for term in english_terms:
            if term not in landscape_template:
                missing_terms.append(term)

        if missing_terms:
            print(f"WARNING: Missing expected English terms in landscape template: {missing_terms}")
            return False

        # Test exploration prompt template
        exploration_template = EXPLORATION_PROMPT
        chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in exploration_template)
        if chinese_chars:
            print("WARNING: Exploration prompt template still contains Chinese characters")
            return False

        # Test exploitation prompt template
        exploitation_template = EXPLOITATION_PROMPT
        chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in exploitation_template)
        if chinese_chars:
            print("WARNING: Exploitation prompt template still contains Chinese characters")
            return False

        print("✓ Experts prompt templates test passed")
        return True

    except Exception as e:
        print(f"✗ Experts prompt templates test failed: {e}")
        return False

def test_mock_llm_interface():
    """Test that mock LLM interface works with English responses"""
    try:
        from api.mock_llm_interface import MockLLMInterface
        
        # Create mock interface
        mock_llm = MockLLMInterface(response_mode='intelligent', debug_mode=False)
        
        # Test response generation
        test_prompt = "Generate strategy assignments for 3 individuals with high landscape complexity"
        response = mock_llm.get_response(test_prompt)
        
        # Validate response
        assert isinstance(response, str), "Response should be a string"
        
        # Try to parse as JSON
        response_data = json.loads(response)
        assert 'strategy_assignments' in response_data, "Response should contain strategy_assignments"
        assert 'global_analysis' in response_data, "Response should contain global_analysis"
        
        # Check for English content in reasoning
        for assignment in response_data['strategy_assignments']:
            reasoning = assignment.get('reasoning', '')
            chinese_chars = any('\u4e00' <= char <= '\u9fff' for char in reasoning)
            if chinese_chars:
                print("WARNING: Mock LLM response still contains Chinese characters")
                return False
        
        print("✓ Mock LLM interface test passed")
        return True
        
    except Exception as e:
        print(f"✗ Mock LLM interface test failed: {e}")
        return False

def main():
    """Run all validation tests"""
    print("=== Prompt Conversion Validation ===")
    print()
    
    tests = [
        test_enhanced_strategy_prompt_generation,
        test_experts_prompt_templates,
        test_mock_llm_interface
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All prompt conversion validation tests passed!")
        return True
    else:
        print("❌ Some tests failed. Please review the conversion.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
