# -*- coding: utf-8 -*-

from numba import jit
import numpy as np
import json
import re
 # Add to import section at top of file
from .trackers.strategy_tracker import NumpyEncoder
# Add to utils.py file
import os
import logging
from core.algorithms.gls_evol_enhanced import tour_cost, tour2route, share_distance, normalize_path
# Make psutil import optional
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Warning: psutil module not found. Memory usage tracking will be disabled.")


class ResponseParser:
    """Unified LLM response parser for handling JSON parsing and data extraction"""

    def __init__(self, logger=None):
        """Initialize response parser

        Args:
            logger: Logger instance, uses default logger if None
        """
        self.logger = logger or logging.getLogger(__name__)

    def extract_json_from_response(self, response):
        """Extract JSON data from response

        Args:
            response (str): LLM response text

        Returns:
            dict: Parsed JSON data, returns None if parsing fails
        """
        try:
            # Try to extract JSON format data
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                json_data = json.loads(json_match.group(1))
                self.logger.info("Successfully extracted data from JSON")
                return json_data

            # Try to parse entire response as JSON directly
            try:
                json_data = json.loads(response.strip())
                self.logger.info("Successfully parsed response as JSON directly")
                return json_data
            except json.JSONDecodeError:
                pass

        except Exception as e:
            self.logger.error(f"Error during JSON parsing: {str(e)}")

        return None

    def extract_path_from_response(self, response, original_tour=None):
        """Extract path data from response

        Args:
            response (str): LLM response text
            original_tour (list): Original path for fallback

        Returns:
            dict: Dictionary containing extracted path and other information
        """
        self.logger.info("Starting to extract path from response")

        try:
            # First try to extract JSON format path
            json_data = self.extract_json_from_response(response)
            if json_data:
                # Standardize key names: convert "new_path" to "new_tour"
                if "new_path" in json_data and "new_tour" not in json_data:
                    json_data["new_tour"] = json_data["new_path"]
                    del json_data["new_path"]

                # Ensure necessary fields are included
                if "new_tour" in json_data:
                    if "cur_cost" not in json_data:
                        json_data["cur_cost"] = 0  # Default cost
                    return json_data

            # Try to extract path list
            path_match = re.search(r'\[\s*(\d+(?:\s*,\s*\d+)*)\s*\]', response)
            if path_match:
                path_str = path_match.group(1)
                path = [int(x.strip()) for x in path_str.split(',')]
                self.logger.info(f"Successfully extracted path list: {path}")
                return {"new_tour": path, "cur_cost": 0}

            # If unable to extract, return original path
            self.logger.warning("Unable to extract path from response, returning original path")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}

        except Exception as e:
            self.logger.error(f"Error extracting path: {str(e)}")
            return {"new_tour": original_tour, "cur_cost": 0} if original_tour else {}

    def extract_strategy_from_response(self, response):
        """Extract strategy data from response

        Args:
            response (str): LLM response text

        Returns:
            dict: Dictionary containing strategy information
        """
        json_data = self.extract_json_from_response(response)
        if json_data:
            # Validate required fields in strategy response
            required_fields = ["global_explore_ratio", "individual_assignments", "rationale"]
            if all(field in json_data for field in required_fields):
                return json_data

        # Return default strategy
        self.logger.warning("Unable to extract strategy information, returning default strategy")
        return {
            "global_explore_ratio": 0.5,
            "individual_assignments": {},
            "rationale": "Default strategy"
        }


def convert_numpy_types(obj):
    """统一的NumPy类型转换函数

    参数:
        obj: 需要转换的对象

    返回:
        转换后的对象
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj


# 添加计算种群多样性的函数
def calculate_population_diversity(population):
    """
    Calculate diversity of a population based on tour similarity
    
    Args:
        population: List of individuals with 'tour' key
        
    Returns:
        float: Diversity measure (0-1)
    """
    if not population or len(population) <= 1:
        return 0.0
        
    # Extract tours and ensure they are lists, not numpy arrays
    tours = []
    for ind in population:
        tour = ind.get('tour', [])
        if isinstance(tour, np.ndarray):
            tour = tour.tolist()
        tours.append(tour)
    
    # Calculate pairwise distances
    n = len(tours)
    distances = np.zeros((n, n))
    
    for i in range(n):
        for j in range(i+1, n):
            try:
                # Use share_distance from gls_evol
                shared = share_distance(tours[i], tours[j])
                # Convert to a distance (1 - similarity)
                tour_length = len(tours[i])
                if tour_length > 0:  # 避免除以零
                    distance = 1.0 - (shared / tour_length)
                    distances[i, j] = distance
                    distances[j, i] = distance
            except Exception as e:
                logging.warning(f"计算多样性时出错: {str(e)}")
                distances[i, j] = 0.0
                distances[j, i] = 0.0
    
    # 简单方法：计算平均距离
    total_distance = np.sum(distances) / (n * (n - 1)) if n > 1 else 0.0
    
    # 高级方法：使用相关性矩阵（如果简单方法失败，则回退到这个）
    if total_distance == 0.0:
        try:
            # Add small epsilon to avoid zero standard deviation
            epsilon = 1e-8
            distances_with_noise = distances + np.random.normal(0, epsilon, distances.shape)
            
            # Use np.nan_to_num to replace NaN with 0 and inf with large finite numbers
            corr_matrix = np.nan_to_num(np.corrcoef(distances_with_noise), nan=0.0, posinf=1.0, neginf=-1.0)
            
            # Calculate diversity as 1 - average correlation
            total_distance = 1.0 - np.mean(np.abs(corr_matrix - np.eye(n)))
        except Exception as e:
            logging.warning(f"计算多样性高级方法失败: {str(e)}")
            # 如果高级方法也失败，使用固定值
            total_distance = 0.5  # 使用中等多样性值作为默认值
    
    return total_distance


def get_memory_usage():
    """获取当前进程的内存使用情况，返回MB为单位的值"""
    if not PSUTIL_AVAILABLE:
        return -1.0  # Return -1 to indicate psutil is not available
    
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    # 转换为MB
    return memory_info.rss / 1024 / 1024


def count_calls(func):
    def wrapper(*args, **kwargs):
        wrapper.call_count += 1
        if(wrapper.call_count%1000==0):
            print(f"函数 {func.__name__} 被调用了 {wrapper.call_count} 次")
        return func(*args, **kwargs)
    
    wrapper.call_count = 0
    return wrapper


# # @count_calls
# # @jit(nopython=True)
# def tour_cost_2End(dis_m, tour2End):
#     c=0
#     s = 0
#     e = tour2End[0,1]
#     for i in range(tour2End.shape[0]):
#         c += dis_m[s,e]
#         s = e
#         e = tour2End[s,1]
#     return c



def convert_to_list(p1):
    if isinstance(p1, np.ndarray):
        return p1.tolist()  # 转换为列表
    elif isinstance(p1, list):
        return p1  # 已经是列表，返回原列表
    else:
        raise TypeError("输入的 p1 必须是 numpy.ndarray 或 list 类型")


def adaptive_cluster_count(population_size, diversity, problem_size):
    """
    自适应调整聚类数量
    
    Args:
        population_size: 种群大小
        diversity: 当前种群多样性
        problem_size: 问题规模（如城市数量）
        
    Returns:
        int: 建议的聚类数量
    """
    # 基础聚类数量，根据种群大小的平方根计算
    base_clusters = int(np.sqrt(population_size))
    
    # 根据多样性调整
    # 多样性高时，增加聚类数；多样性低时，减少聚类数
    diversity_factor = 0.5 + diversity
    
    # 根据问题规模调整
    # 问题规模越大，聚类数量相对增加
    size_factor = 0.5 + min(1.0, problem_size / 100)
    
    # 计算最终聚类数
    adjusted_clusters = int(base_clusters * diversity_factor * size_factor)
    
    # 设置上下限
    min_clusters = 2
    max_clusters = min(20, population_size // 5)  # 最多20个聚类，且每个聚类至少5个个体
    
    return max(min_clusters, min(adjusted_clusters, max_clusters))

def calculate_distance_matrix_for_clustering(population):
    """
    计算种群个体间的距离矩阵，用于聚类
    
    Args:
        population: 种群列表，每个个体包含tour
        
    Returns:
        numpy.ndarray: 距离矩阵
    """
    import numpy as np
    
    pop_size = len(population)
    distance_matrix = np.zeros((pop_size, pop_size))
    
    for i in range(pop_size):
        for j in range(i+1, pop_size):
            tour1 = population[i]["tour"]
            tour2 = population[j]["tour"]
            
            # 确保tour是列表
            if isinstance(tour1, np.ndarray):
                tour1 = tour1.tolist()
            if isinstance(tour2, np.ndarray):
                tour2 = tour2.tolist()
            
            # 使用共享距离作为相似度度量
            shared_edges = share_distance(tour1, tour2)
            # 转换为距离（1-相似度）
            distance = 1.0 - (shared_edges / len(tour1))
            
            distance_matrix[i, j] = distance
            distance_matrix[j, i] = distance
    
    return distance_matrix


def calculate_diversity_for_tours(tours):
    """
    计算一组解的多样性
    
    Args:
        tours: 解的列表，每个解是一个路径
        
    Returns:
        float: 多样性指标，值越大表示多样性越高
    """
    if len(tours) <= 1:
        return 0.0
    
    import numpy as np
    
    total_distance = 0.0
    count = 0
    
    for i in range(len(tours)):
        for j in range(i+1, len(tours)):
            tour1 = tours[i]
            tour2 = tours[j]
            
            # 确保tour是列表
            if isinstance(tour1, np.ndarray):
                tour1 = tour1.tolist()
            if isinstance(tour2, np.ndarray):
                tour2 = tour2.tolist()
            
            # 使用gls_evol中的share_distance计算共享边数量
            try:
                shared_edges = share_distance(tour1, tour2)
                
                # 计算不相似度（1减去共享边比例）
                tour_length = len(tour1)
                distance = 1.0 - (shared_edges / tour_length)
                
                total_distance += distance
                count += 1
            except Exception as e:
                print(f"计算共享距离时出错: {e}")
    
    # 返回平均距离，值越大表示多样性越高
    return total_distance / count if count > 0 else 0.0


def save_optimization_results(res_populations, instance_name, output_base_dir, logger=None):
    """
    保存优化结果到JSON和solution文件

    Args:
        res_populations (list): 精英解种群列表
        instance_name (str): 实例名称
        output_base_dir (str): 输出基础目录
        logger (logging.Logger, optional): 日志记录器

    Returns:
        dict: 包含保存文件路径的字典 {"json_path": str, "solution_path": str}
    """
    import os
    import json
    from datetime import datetime
    from core.algorithms.gls_evol_enhanced import tour_cost, tour2route

    if logger is None:
        import logging
        logger = logging.getLogger(__name__)

    try:
        # 1. 准备结果数据
        if not res_populations:
            logger.warning(f"实例 {instance_name} 没有有效的解集")
            return {"json_path": None, "solution_path": None}

        # 构建JSON格式的结果
        final_result = {
            "instance_name": instance_name,
            "best_cost": min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"],
            "best_tour": min(res_populations, key=lambda x: x["cur_cost"])["tour"],
            "elite_solutions": [{
                "cost": sol["cur_cost"],
                "tour": sol["tour"]
            } for sol in res_populations[:3]]
        }

        # 2. 保存JSON格式结果
        result_dir = os.path.join(output_base_dir, "results", "solutions")
        os.makedirs(result_dir, exist_ok=True)
        json_path = os.path.join(result_dir, f"{instance_name}_solution.json")

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)

        # 3. 保存solution格式结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        solution_path = os.path.join(result_dir, f"{instance_name}_{timestamp}.solution")

        with open(solution_path, 'w', encoding='utf-8') as f:
            # 对精英解按成本排序
            sorted_solutions = sorted(res_populations, key=lambda x: x["cur_cost"])

            if sorted_solutions:
                # 获取最优成本
                best_cost = int(round(sorted_solutions[0]['cur_cost']))

                # 写入每个解
                for sol in sorted_solutions:
                    # 确保成本值为整数
                    cost = int(sol["cur_cost"]) if isinstance(sol["cur_cost"], (int, float)) else int(round(sol["cur_cost"]))

                    # 只保存成本最优的路径
                    if cost > best_cost:
                        break  # 因为已排序，后续成本都会更大

                    # 确保路径以0开始
                    tour = sol["tour"]

                    # 使用gls_evol_enhanced中的normalize_path函数，确保它返回有效的路径
                    normalized_tour = normalize_path(tour)
                    if normalized_tour is None:
                        # 如果normalize_path返回None，则使用原始tour
                        normalized_tour = tour

                    # 转换为字符串
                    tour_str = ' '.join(str(node) for node in normalized_tour)
                    line = f"{cost} {tour_str}\n"
                    f.write(line)

        logger.info(f"最终结果已保存到: {json_path}")
        logger.info(f"解集文件已保存到: {solution_path}")

        return {"json_path": json_path, "solution_path": solution_path}

    except Exception as e:
        logger.error(f"保存实例 {instance_name} 结果时出错: {str(e)}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return {"json_path": None, "solution_path": None}


def save_solutions(instance_name, res_populations, output_dir=None):
    """
    保存解集到指定文件夹 (保持向后兼容性)

    Args:
        instance_name: 实例名称
        res_populations: 解集
        output_dir: 输出目录，默认为 results/solutions

    Returns:
        str: 保存的文件路径
    """
    import os
    from datetime import datetime

    # 如果未指定输出目录，使用默认目录
    if output_dir is None:
        # 获取src根目录
        src_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        output_dir = os.path.join(src_root, "results", "solutions")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 构建输出文件路径
    output_file = os.path.join(output_dir, f"{instance_name}_{timestamp}.solution")

    # 写入解集 - 修改格式：去掉序号，只保留成本和路径
    with open(output_file, 'w', encoding='utf-8') as f:
        for solution in res_populations:
            # 获取解的成本
            cost = solution.get('cur_cost', 0)
            # 获取路径
            tour = ' '.join(map(str, solution.get('tour', [])))
            # 按照新格式写入: 成本 [路径]
            f.write(f"{cost} {tour}\n")

    return output_file