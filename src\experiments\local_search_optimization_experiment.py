#!/usr/bin/env python3
"""
局部搜索核心循环优化效果对比实验
基于选中的while循环代码进行原始版本vs优化版本的性能对比
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import psutil
import random
import copy
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from collections import defaultdict
import pickle
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from core.algorithms import gls_operators
from core.algorithms.gls_evol_enhanced import (
    tour_cost, tour_cost_2End, route2tour, tour2route, 
    is_valid_tsp_tour_fast, perturb_reorder
)
from core.data.loadinstance import load_instance
from core.data.initpop import INIT
from config.config import GLOBAL_INPUT_PATH, GLOBAL_OUTPUT_PATH

@dataclass
class ExperimentMetrics:
    """实验性能指标"""
    # 基本信息
    instance_name: str = ""
    algorithm_type: str = ""  # 'original' or 'optimized'
    run_id: int = 0
    
    # 执行时间指标
    total_execution_time: float = 0.0
    local_search_loop_time: float = 0.0
    two_opt_time: float = 0.0
    relocate_time: float = 0.0
    time_recording_overhead: float = 0.0
    
    # 内存使用指标
    peak_memory_mb: float = 0.0
    avg_memory_mb: float = 0.0
    memory_samples: List[float] = field(default_factory=list)
    
    # 搜索质量指标
    initial_cost: float = 0.0
    final_cost: float = 0.0
    best_cost: float = 0.0
    improvement_ratio: float = 0.0
    
    # 算法行为指标
    total_iterations: int = 0
    improvement_iterations: int = 0
    two_opt_improvements: int = 0
    relocate_improvements: int = 0
    early_stop_triggered: bool = False
    invalid_search_count: int = 0
    
    # 效率指标
    improvements_per_second: float = 0.0
    invalid_search_ratio: float = 0.0

class SmartImprovementDetector:
    """智能改进检测器"""
    
    def __init__(self, min_improvement_threshold=1e-6, stagnation_limit=15):
        self.min_improvement_threshold = min_improvement_threshold
        self.stagnation_limit = stagnation_limit
        self.stagnation_count = 0
        self.total_improvement = 0
        
    def should_continue_search(self, delta, iteration_count):
        """判断是否应该继续搜索"""
        if delta < -self.min_improvement_threshold:
            # 有意义的改进
            self.stagnation_count = 0
            self.total_improvement += -delta
            return True, 'significant_improvement'
        elif delta < 0:
            # 微小改进
            self.stagnation_count += 1
            self.total_improvement += -delta
            return self.stagnation_count < self.stagnation_limit, 'minor_improvement'
        else:
            # 无改进
            self.stagnation_count += 1
            if self.stagnation_count >= self.stagnation_limit:
                return False, 'stagnation_limit_reached'
            return True, 'no_improvement'

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, enable_memory_monitoring=True):
        self.enable_memory_monitoring = enable_memory_monitoring
        self.metrics = ExperimentMetrics()
        self.start_time = None
        self.process = psutil.Process() if enable_memory_monitoring else None
        
    def start_profiling(self, instance_name, algorithm_type, run_id):
        """开始性能分析"""
        self.metrics.instance_name = instance_name
        self.metrics.algorithm_type = algorithm_type
        self.metrics.run_id = run_id
        self.start_time = time.time()
        
    def record_memory_usage(self):
        """记录内存使用"""
        if self.process:
            try:
                memory_mb = self.process.memory_info().rss / 1024 / 1024
                self.metrics.memory_samples.append(memory_mb)
            except:
                pass
                
    def end_profiling(self):
        """结束性能分析"""
        if self.start_time:
            self.metrics.total_execution_time = time.time() - self.start_time
            
        if self.metrics.memory_samples:
            self.metrics.peak_memory_mb = max(self.metrics.memory_samples)
            self.metrics.avg_memory_mb = np.mean(self.metrics.memory_samples)
            
        # 计算派生指标
        if self.metrics.initial_cost > 0:
            self.metrics.improvement_ratio = (
                (self.metrics.initial_cost - self.metrics.final_cost) / 
                self.metrics.initial_cost * 100
            )
            
        if self.metrics.total_execution_time > 0:
            self.metrics.improvements_per_second = (
                self.metrics.improvement_iterations / self.metrics.total_execution_time
            )
            
        if self.metrics.total_iterations > 0:
            self.metrics.invalid_search_ratio = (
                self.metrics.invalid_search_count / self.metrics.total_iterations * 100
            )

def original_local_search_loop(cur_route, cur_cost, D, N, end_time, profiler):
    """原始版本的局部搜索循环（包含所有性能问题）"""
    improved = True
    iteration_count = 0
    
    loop_start_time = time.time()
    
    while improved and time.time() < end_time:
        improved = False
        profiler.record_memory_usage()
        
        # 原始版本：重复的时间记录开销
        two_opt_start = time.time()  # 第1次time.time()调用
        
        delta, new_tour = gls_operators.two_opt_a2a_with_evaluation(cur_route, D, N, False)
        
        # 原始版本：包含delta==0判断和np.array_equal比较
        if delta < 0 or (delta == 0 and not np.array_equal(new_tour, cur_route)):
            improved = True
            # 原始版本：每次改进都重新计算完整路径成本
            cur_cost = tour_cost_2End(D, new_tour)
            cur_route = new_tour
            profiler.metrics.two_opt_improvements += 1
            profiler.metrics.improvement_iterations += 1
        else:
            profiler.metrics.invalid_search_count += 1
            
        # 原始版本：重复的时间记录
        elapsed_time = time.time() - two_opt_start  # 第2次time.time()调用
        profiler.metrics.two_opt_time += elapsed_time
        
        # Relocate算子 - 同样的问题模式
        relocate_start = time.time()  # 第3次time.time()调用
        
        delta, new_tour = gls_operators.relocate_a2a_with_evaluation(cur_route, D, N, False)
        
        if delta < 0 or (delta == 0 and not np.array_equal(new_tour, cur_route)):
            improved = True
            cur_cost = tour_cost_2End(D, new_tour)  # 重复计算成本
            cur_route = new_tour
            profiler.metrics.relocate_improvements += 1
            profiler.metrics.improvement_iterations += 1
        else:
            profiler.metrics.invalid_search_count += 1
            
        elapsed_time = time.time() - relocate_start  # 第4次time.time()调用
        profiler.metrics.relocate_time += elapsed_time
        
        iteration_count += 1
        profiler.metrics.total_iterations = iteration_count
        
        # 模拟额外的时间记录开销（原始版本中存在的）
        _ = time.time()  # 第5次无用的time.time()调用
        
    profiler.metrics.local_search_loop_time = time.time() - loop_start_time
    return cur_route, cur_cost

def optimized_local_search_loop(cur_route, cur_cost, D, N, end_time, profiler):
    """优化版本的局部搜索循环"""
    improvement_detector = SmartImprovementDetector()
    iteration_count = 0
    
    loop_start_time = time.time()
    
    while iteration_count < 100 and time.time() < end_time:  # 添加迭代限制
        profiler.record_memory_usage()
        
        best_delta = 0
        best_new_tour = None
        best_operator = None
        loop_improved = False
        
        # 优化版本：减少时间记录，只在需要时记录
        op_start_time = time.time() if profiler.metrics.algorithm_type == 'optimized_detailed' else None
        
        # Two-opt算子
        delta, new_tour = gls_operators.two_opt_a2a_with_evaluation(cur_route, D, N, False)
        if delta < best_delta:
            best_delta = delta
            best_new_tour = new_tour
            best_operator = 'two_opt'
            loop_improved = True
        
        # Relocate算子
        delta, new_tour = gls_operators.relocate_a2a_with_evaluation(cur_route, D, N, False)
        if delta < best_delta:
            best_delta = delta
            best_new_tour = new_tour
            best_operator = 'relocate'
            loop_improved = True
        
        if op_start_time:
            total_op_time = time.time() - op_start_time
            if best_operator == 'two_opt':
                profiler.metrics.two_opt_time += total_op_time
            else:
                profiler.metrics.relocate_time += total_op_time
        
        # 智能改进检测
        should_continue, improvement_type = improvement_detector.should_continue_search(
            best_delta, iteration_count
        )
        
        # 应用最佳改进
        if loop_improved and best_delta < 0:
            cur_route = best_new_tour
            cur_cost += best_delta  # 优化版本：增量更新成本
            profiler.metrics.improvement_iterations += 1
            
            if best_operator == 'two_opt':
                profiler.metrics.two_opt_improvements += 1
            else:
                profiler.metrics.relocate_improvements += 1
        else:
            profiler.metrics.invalid_search_count += 1
        
        # 检查是否应该继续
        if not should_continue:
            profiler.metrics.early_stop_triggered = True
            break
        
        iteration_count += 1
        profiler.metrics.total_iterations = iteration_count
        
    profiler.metrics.local_search_loop_time = time.time() - loop_start_time
    return cur_route, cur_cost

class LocalSearchExperiment:
    """局部搜索实验类"""
    
    def __init__(self):
        self.test_instances = {
            'small_scale': [
                {'name': 'eil51', 'size': 51},
                {'name': 'berlin52', 'size': 52}
            ],
            'medium_scale': [
                {'name': 'st70', 'size': 70},
                {'name': 'kroA100', 'size': 100}
            ],
            'large_scale': [
                {'name': 'lin105', 'size': 105},
                {'name': 'ch130', 'size': 130}
            ]
        }
        self.results = []
        
    def load_test_instance(self, instance_name):
        """加载测试实例"""
        try:
            # 尝试从TSPLIB加载
            instance_file = os.path.join(GLOBAL_INPUT_PATH, f"{instance_name}.tsp")
            if os.path.exists(instance_file):
                distance_matrix, coordinates = load_instance(instance_file)
                initial_tour = INIT.mixed_init(distance_matrix, 1)[0]['tour']
                return {
                    'distance_matrix': distance_matrix,
                    'coordinates': coordinates,
                    'initial_tour': initial_tour,
                    'initial_cost': tour_cost(distance_matrix, initial_tour)
                }
        except Exception as e:
            print(f"加载实例 {instance_name} 失败: {e}")
            
        # 生成随机实例作为备选
        return self.generate_random_instance(instance_name)
    
    def generate_random_instance(self, instance_name):
        """生成随机测试实例"""
        size_map = {'eil51': 51, 'berlin52': 52, 'st70': 70, 'kroA100': 100, 'lin105': 105, 'ch130': 130}
        size = size_map.get(instance_name, 50)
        
        # 生成随机坐标
        np.random.seed(42)  # 固定种子确保可重现
        coordinates = np.random.rand(size, 2) * 1000
        
        # 计算距离矩阵
        distance_matrix = np.zeros((size, size))
        for i in range(size):
            for j in range(size):
                if i != j:
                    distance_matrix[i][j] = np.sqrt(
                        (coordinates[i][0] - coordinates[j][0])**2 + 
                        (coordinates[i][1] - coordinates[j][1])**2
                    )
        
        # 生成初始解
        initial_tour = np.arange(size, dtype=np.int64)
        np.random.shuffle(initial_tour[1:])  # 保持起点为0
        
        return {
            'distance_matrix': distance_matrix,
            'coordinates': coordinates,
            'initial_tour': initial_tour,
            'initial_cost': tour_cost(distance_matrix, initial_tour)
        }
    
    def run_single_experiment(self, instance_name, instance_data, algorithm_type, run_id):
        """运行单次实验"""
        profiler = PerformanceProfiler(enable_memory_monitoring=True)
        profiler.start_profiling(instance_name, algorithm_type, run_id)
        
        # 设置实验参数
        distance_matrix = instance_data['distance_matrix']
        initial_tour = instance_data['initial_tour'].copy()
        initial_cost = instance_data['initial_cost']
        
        profiler.metrics.initial_cost = initial_cost
        profiler.metrics.best_cost = initial_cost
        
        # 准备算法输入
        N = np.argsort(distance_matrix, axis=1)[:, 1:].astype(np.int64)
        cur_route = tour2route(initial_tour)
        cur_cost = initial_cost
        
        # 设置时间限制
        time_limit = 10.0  # 10秒时间限制
        end_time = time.time() + time_limit
        
        try:
            if algorithm_type == 'original':
                final_route, final_cost = original_local_search_loop(
                    cur_route, cur_cost, distance_matrix, N, end_time, profiler
                )
            else:  # optimized
                final_route, final_cost = optimized_local_search_loop(
                    cur_route, cur_cost, distance_matrix, N, end_time, profiler
                )
            
            profiler.metrics.final_cost = final_cost
            profiler.metrics.best_cost = min(profiler.metrics.best_cost, final_cost)
            
        except Exception as e:
            print(f"实验执行失败: {instance_name}, {algorithm_type}, run={run_id}, error={e}")
            return None
        
        profiler.end_profiling()
        return profiler.metrics

    def run_comprehensive_experiment(self, runs_per_instance=5):
        """运行综合对比实验"""
        print("=" * 80)
        print("局部搜索核心循环优化效果对比实验")
        print("=" * 80)

        total_experiments = 0
        completed_experiments = 0

        # 计算总实验数量
        for scale_instances in self.test_instances.values():
            total_experiments += len(scale_instances) * 2 * runs_per_instance

        print(f"总实验数量: {total_experiments}")
        print(f"每个实例运行次数: {runs_per_instance}")

        # 执行实验
        for scale, instances in self.test_instances.items():
            print(f"\n开始 {scale} 实验...")

            for instance_info in instances:
                instance_name = instance_info['name']
                print(f"  处理实例: {instance_name} (规模: {instance_info['size']})")

                try:
                    # 加载实例数据
                    instance_data = self.load_test_instance(instance_name)

                    # 对每个算法类型运行多次实验
                    for algorithm_type in ['original', 'optimized']:
                        print(f"    算法类型: {algorithm_type}")

                        for run_id in range(runs_per_instance):
                            # 设置随机种子确保可重现性
                            seed = 42 + run_id
                            np.random.seed(seed)
                            random.seed(seed)

                            result = self.run_single_experiment(
                                instance_name, instance_data, algorithm_type, run_id
                            )

                            if result:
                                self.results.append(result)
                                completed_experiments += 1

                                # 显示进度
                                progress = completed_experiments / total_experiments * 100
                                print(f"      运行 {run_id+1}/{runs_per_instance} "
                                      f"完成 (总进度: {progress:.1f}%)")
                            else:
                                print(f"      运行 {run_id+1} 失败")

                except Exception as e:
                    print(f"    实例 {instance_name} 处理失败: {e}")
                    continue

        print(f"\n实验完成! 成功完成 {completed_experiments}/{total_experiments} 个实验")
        return self.results

class ExperimentAnalyzer:
    """实验结果分析器"""

    def __init__(self, results):
        self.results = results
        self.df = self._convert_to_dataframe()

    def _convert_to_dataframe(self):
        """转换为DataFrame格式"""
        data = []
        for result in self.results:
            row = {
                'instance_name': result.instance_name,
                'algorithm_type': result.algorithm_type,
                'run_id': result.run_id,
                'total_execution_time': result.total_execution_time,
                'local_search_loop_time': result.local_search_loop_time,
                'two_opt_time': result.two_opt_time,
                'relocate_time': result.relocate_time,
                'peak_memory_mb': result.peak_memory_mb,
                'avg_memory_mb': result.avg_memory_mb,
                'initial_cost': result.initial_cost,
                'final_cost': result.final_cost,
                'improvement_ratio': result.improvement_ratio,
                'total_iterations': result.total_iterations,
                'improvement_iterations': result.improvement_iterations,
                'two_opt_improvements': result.two_opt_improvements,
                'relocate_improvements': result.relocate_improvements,
                'early_stop_triggered': result.early_stop_triggered,
                'invalid_search_count': result.invalid_search_count,
                'improvements_per_second': result.improvements_per_second,
                'invalid_search_ratio': result.invalid_search_ratio
            }
            data.append(row)

        return pd.DataFrame(data)

    def generate_performance_comparison_report(self):
        """生成性能对比报告"""
        print("\n" + "=" * 60)
        print("性能对比分析报告")
        print("=" * 60)

        # 按算法类型分组统计
        grouped = self.df.groupby('algorithm_type').agg({
            'total_execution_time': ['mean', 'std', 'min', 'max'],
            'local_search_loop_time': ['mean', 'std'],
            'peak_memory_mb': ['mean', 'std'],
            'improvement_ratio': ['mean', 'std'],
            'total_iterations': ['mean', 'std'],
            'improvement_iterations': ['mean', 'std'],
            'improvements_per_second': ['mean', 'std'],
            'invalid_search_ratio': ['mean', 'std'],
            'early_stop_triggered': 'sum'
        }).round(4)

        print("\n整体性能统计:")
        print(grouped)

        # 计算改进百分比
        original_stats = grouped.loc['original']
        optimized_stats = grouped.loc['optimized']

        print(f"\n关键性能指标改进:")

        # 执行时间改进
        original_time = original_stats[('total_execution_time', 'mean')]
        optimized_time = optimized_stats[('total_execution_time', 'mean')]
        time_improvement = (original_time - optimized_time) / original_time * 100
        print(f"  总执行时间减少: {time_improvement:.2f}%")

        # 局部搜索循环时间改进
        original_loop_time = original_stats[('local_search_loop_time', 'mean')]
        optimized_loop_time = optimized_stats[('local_search_loop_time', 'mean')]
        loop_time_improvement = (original_loop_time - optimized_loop_time) / original_loop_time * 100
        print(f"  局部搜索循环时间减少: {loop_time_improvement:.2f}%")

        # 内存使用改进
        original_memory = original_stats[('peak_memory_mb', 'mean')]
        optimized_memory = optimized_stats[('peak_memory_mb', 'mean')]
        memory_improvement = (original_memory - optimized_memory) / original_memory * 100
        print(f"  峰值内存使用减少: {memory_improvement:.2f}%")

        # 搜索效率改进
        original_efficiency = original_stats[('improvements_per_second', 'mean')]
        optimized_efficiency = optimized_stats[('improvements_per_second', 'mean')]
        efficiency_improvement = (optimized_efficiency - original_efficiency) / original_efficiency * 100
        print(f"  搜索效率提升: {efficiency_improvement:.2f}%")

        # 无效搜索减少
        original_invalid = original_stats[('invalid_search_ratio', 'mean')]
        optimized_invalid = optimized_stats[('invalid_search_ratio', 'mean')]
        invalid_reduction = (original_invalid - optimized_invalid) / original_invalid * 100
        print(f"  无效搜索比例减少: {invalid_reduction:.2f}%")

        # 早停触发情况
        optimized_early_stops = optimized_stats[('early_stop_triggered', 'sum')]
        total_optimized_runs = len(self.df[self.df['algorithm_type'] == 'optimized'])
        early_stop_rate = optimized_early_stops / total_optimized_runs * 100
        print(f"  优化版本早停触发率: {early_stop_rate:.1f}%")

        return {
            'time_improvement': time_improvement,
            'loop_time_improvement': loop_time_improvement,
            'memory_improvement': memory_improvement,
            'efficiency_improvement': efficiency_improvement,
            'invalid_reduction': invalid_reduction,
            'early_stop_rate': early_stop_rate
        }

    def generate_scalability_analysis(self):
        """生成可扩展性分析"""
        print("\n" + "=" * 60)
        print("可扩展性分析报告")
        print("=" * 60)

        # 按实例规模分析
        scale_mapping = {}
        for scale, instances in {
            'small_scale': [{'name': 'eil51'}, {'name': 'berlin52'}],
            'medium_scale': [{'name': 'st70'}, {'name': 'kroA100'}],
            'large_scale': [{'name': 'lin105'}, {'name': 'ch130'}]
        }.items():
            for instance in instances:
                scale_mapping[instance['name']] = scale

        self.df['scale'] = self.df['instance_name'].map(scale_mapping)

        # 按规模和算法类型分组
        scale_analysis = self.df.groupby(['scale', 'algorithm_type']).agg({
            'total_execution_time': 'mean',
            'improvement_ratio': 'mean',
            'peak_memory_mb': 'mean',
            'improvements_per_second': 'mean'
        }).round(4)

        print("\n按问题规模的性能分析:")
        print(scale_analysis)

        # 计算各规模下的改进效果
        print(f"\n各规模问题的优化效果:")
        for scale in ['small_scale', 'medium_scale', 'large_scale']:
            if scale in scale_analysis.index.get_level_values(0):
                original_time = scale_analysis.loc[(scale, 'original'), 'total_execution_time']
                optimized_time = scale_analysis.loc[(scale, 'optimized'), 'total_execution_time']
                improvement = (original_time - optimized_time) / original_time * 100

                original_memory = scale_analysis.loc[(scale, 'original'), 'peak_memory_mb']
                optimized_memory = scale_analysis.loc[(scale, 'optimized'), 'peak_memory_mb']
                memory_improvement = (original_memory - optimized_memory) / original_memory * 100

                print(f"  {scale}:")
                print(f"    执行时间改进: {improvement:.2f}%")
                print(f"    内存使用改进: {memory_improvement:.2f}%")

    def generate_statistical_significance_test(self):
        """生成统计显著性检验"""
        print("\n" + "=" * 60)
        print("统计显著性检验")
        print("=" * 60)

        try:
            from scipy import stats

            original_data = self.df[self.df['algorithm_type'] == 'original']
            optimized_data = self.df[self.df['algorithm_type'] == 'optimized']

            metrics_to_test = [
                'total_execution_time', 'local_search_loop_time', 'peak_memory_mb',
                'improvement_ratio', 'improvements_per_second', 'invalid_search_ratio'
            ]

            print(f"\n对 {len(metrics_to_test)} 个关键指标进行t检验:")

            for metric in metrics_to_test:
                original_values = original_data[metric].values
                optimized_values = optimized_data[metric].values

                # 执行独立样本t检验
                t_stat, p_value = stats.ttest_ind(original_values, optimized_values)

                # 计算效应大小 (Cohen's d)
                pooled_std = np.sqrt(((len(original_values) - 1) * np.var(original_values, ddof=1) +
                                     (len(optimized_values) - 1) * np.var(optimized_values, ddof=1)) /
                                    (len(original_values) + len(optimized_values) - 2))

                if pooled_std > 0:
                    cohens_d = abs(np.mean(original_values) - np.mean(optimized_values)) / pooled_std
                else:
                    cohens_d = 0

                # 显著性标记
                if p_value < 0.001:
                    significance = "***"
                elif p_value < 0.01:
                    significance = "**"
                elif p_value < 0.05:
                    significance = "*"
                else:
                    significance = "ns"

                # 效应大小解释
                if cohens_d < 0.2:
                    effect_size = "小"
                elif cohens_d < 0.5:
                    effect_size = "中等"
                elif cohens_d < 0.8:
                    effect_size = "大"
                else:
                    effect_size = "非常大"

                print(f"\n  {metric}:")
                print(f"    t统计量: {t_stat:.4f}")
                print(f"    p值: {p_value:.6f} {significance}")
                print(f"    效应大小: {cohens_d:.4f} ({effect_size})")

        except ImportError:
            print("警告: 无法导入scipy，跳过统计显著性检验")

    def save_detailed_results(self, filename_prefix="local_search_experiment"):
        """保存详细实验结果"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")

        # 保存CSV格式的详细数据
        csv_filename = f"{filename_prefix}_{timestamp}.csv"
        self.df.to_csv(csv_filename, index=False)
        print(f"\n详细实验数据已保存到: {csv_filename}")

        # 保存JSON格式的汇总统计
        summary_stats = {}
        for algorithm_type in ['original', 'optimized']:
            type_data = self.df[self.df['algorithm_type'] == algorithm_type]
            summary_stats[algorithm_type] = {
                'total_execution_time': {
                    'mean': float(type_data['total_execution_time'].mean()),
                    'std': float(type_data['total_execution_time'].std()),
                    'min': float(type_data['total_execution_time'].min()),
                    'max': float(type_data['total_execution_time'].max())
                },
                'improvement_ratio': {
                    'mean': float(type_data['improvement_ratio'].mean()),
                    'std': float(type_data['improvement_ratio'].std())
                },
                'peak_memory_mb': {
                    'mean': float(type_data['peak_memory_mb'].mean()),
                    'std': float(type_data['peak_memory_mb'].std())
                }
            }

        json_filename = f"{filename_prefix}_summary_{timestamp}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(summary_stats, f, indent=2, ensure_ascii=False)
        print(f"汇总统计已保存到: {json_filename}")

        return csv_filename, json_filename

def main():
    """主实验执行函数"""
    print("开始局部搜索核心循环优化效果对比实验...")

    # 创建实验对象
    experiment = LocalSearchExperiment()

    # 运行实验
    start_time = time.time()
    results = experiment.run_comprehensive_experiment(runs_per_instance=5)
    end_time = time.time()

    print(f"\n实验总耗时: {end_time - start_time:.2f} 秒")
    print(f"收集到 {len(results)} 个有效实验结果")

    if not results:
        print("没有收集到有效的实验结果，实验终止")
        return

    # 分析结果
    analyzer = ExperimentAnalyzer(results)

    # 生成各种分析报告
    performance_metrics = analyzer.generate_performance_comparison_report()
    analyzer.generate_scalability_analysis()
    analyzer.generate_statistical_significance_test()

    # 保存详细结果
    csv_file, json_file = analyzer.save_detailed_results()

    # 输出实验总结
    print("\n" + "=" * 80)
    print("实验总结")
    print("=" * 80)
    print(f"✓ 总执行时间改进: {performance_metrics['time_improvement']:.2f}%")
    print(f"✓ 局部搜索循环时间改进: {performance_metrics['loop_time_improvement']:.2f}%")
    print(f"✓ 内存使用改进: {performance_metrics['memory_improvement']:.2f}%")
    print(f"✓ 搜索效率提升: {performance_metrics['efficiency_improvement']:.2f}%")
    print(f"✓ 无效搜索减少: {performance_metrics['invalid_reduction']:.2f}%")
    print(f"✓ 优化版本早停触发率: {performance_metrics['early_stop_rate']:.1f}%")

    print(f"\n详细结果文件:")
    print(f"  - CSV数据: {csv_file}")
    print(f"  - JSON汇总: {json_file}")

    # 验证预期效果
    print(f"\n预期效果验证:")
    if performance_metrics['time_improvement'] >= 30:
        print("✓ 执行时间改进达到预期 (≥30%)")
    else:
        print(f"⚠ 执行时间改进未达预期 ({performance_metrics['time_improvement']:.2f}% < 30%)")

    if performance_metrics['memory_improvement'] >= 20:
        print("✓ 内存使用改进达到预期 (≥20%)")
    else:
        print(f"⚠ 内存使用改进未达预期 ({performance_metrics['memory_improvement']:.2f}% < 20%)")

    if performance_metrics['efficiency_improvement'] >= 15:
        print("✓ 搜索效率提升达到预期 (≥15%)")
    else:
        print(f"⚠ 搜索效率提升未达预期 ({performance_metrics['efficiency_improvement']:.2f}% < 15%)")

    print("\n实验完成!")

if __name__ == "__main__":
    main()
