# -*- coding: utf-8 -*-
"""
Enhanced Strategy Selection Prompt Engineering Templates

Provides optimized LLM prompt templates for individual-level strategy selection,
fully utilizing fitness landscape analysis and individual state information.
"""

import json
import re
from typing import Dict, List, Any, Optional


# Individual-level strategy selection prompt template
INDIVIDUAL_STRATEGY_PROMPT_TEMPLATE = """## Role: TSP Optimization Strategy Selection Expert

### Task Overview
Based on detailed individual state analysis and fitness landscape characteristics, assign optimal strategies for each individual in the TSP optimization algorithm.

### Current State (Iteration {iteration})

#### Landscape Feature Analysis
- **Global Ruggedness**: {global_ruggedness:.3f} (0-1, higher values indicate more rugged landscape with more local optima)
- **Modality**: {modality} (unimodal/multimodal, affects search strategy selection)
- **Deceptiveness**: {deceptiveness} (low/medium/high, high deceptiveness requires stronger exploration)
- **Gradient Strength**: {gradient_strength:.3f} (0-1, higher values indicate more reliable gradient information)
- **Population Diversity**: {population_diversity:.3f} (0-1, higher values indicate more diversified population)
- **Convergence Trend**: {convergence_trend:.3f} (0-1, higher values indicate more converged population)
- **Evolution Phase**: {evolution_phase} (exploration/exploitation/balance)
- **Difficult Regions**: {difficult_regions_count} regions (search areas to avoid)
- **Opportunity Regions**: {opportunity_regions_count} regions (promising search areas)

#### Historical Feedback
{feedback_summary}

### Detailed Individual State Analysis

Population Size: {population_size}

{individual_details}

### Strategy Type Detailed Explanation

#### Exploration Strategies
1. **strong_exploration**: Strong Exploration Strategy
   - Applicable Scenarios: Severe stagnation (>10 generations), extremely low diversity (<0.2), highly deceptive landscape
   - Characteristics: Large-scale mutation, random restart, region jumping
   - Risks: May destroy existing good solutions, high computational cost

2. **balanced_exploration**: Balanced Exploration Strategy
   - Applicable Scenarios: Moderate stagnation (3-10 generations), low-medium diversity (0.2-0.4), medium ruggedness
   - Characteristics: Moderate mutation, regional exploration, maintaining certain randomness
   - Risks: Exploration intensity may be insufficient

3. **intelligent_exploration**: Intelligent Exploration Strategy
   - Applicable Scenarios: Promising medium-quality individuals, many opportunity regions, reliable gradient information
   - Characteristics: Gradient-based exploration, utilizing opportunity regions, adaptive step size
   - Risks: Depends on landscape analysis accuracy

#### Exploitation Strategies
4. **cautious_exploitation**: Cautious Exploitation Strategy
   - Applicable Scenarios: Good individuals (top 30%), slight stagnation, uncertain landscape
   - Characteristics: Small-step local search, conservative optimization, diversity protection
   - Risks: Slow improvement rate

5. **moderate_exploitation**: Moderate Exploitation Strategy
   - Applicable Scenarios: Excellent individuals (top 20%), no stagnation, medium convergence
   - Characteristics: Standard local search, neighborhood optimization, elite learning
   - Risks: May fall into local optima

6. **aggressive_exploitation**: Aggressive Exploitation Strategy
   - Applicable Scenarios: Top individuals (top 10%), strong gradient information, high convergence phase
   - Characteristics: Deep local search, multi-neighborhood combination, concentrated optimization
   - Risks: Over-exploitation, loss of diversity

7. **intensive_exploitation**: Intensive Exploitation Strategy
   - Applicable Scenarios: Optimal individuals (top 5%), deterministic landscape, late convergence
   - Characteristics: Fine-tuning, parameter adjustment, extreme optimization
   - Risks: Concentrated computational resources, insufficient exploration in other regions

#### Hybrid Strategies
8. **adaptive_hybrid**: Adaptive Hybrid Strategy
   - Applicable Scenarios: Complex multimodal landscape, dynamic environment, uncertain situations
   - Characteristics: Dynamic balance of exploration and exploitation, adaptive parameters, multi-strategy fusion
   - Risks: High complexity, difficult parameter tuning

9. **collaborative_escape**: Collaborative Escape Strategy
   - Applicable Scenarios: Collective stagnation, all individuals trapped in local optima, emergency situations
   - Characteristics: Population recombination, collaborative mutation, collective jumping
   - Risks: Highly destructive, requires careful use

### Strategy Selection Principles

#### Core Principles
1. **Fitness Principle**:
   - Top individuals (top 10%): Bias toward exploitation strategies, maintain advantage
   - Excellent individuals (top 30%): Balanced strategies, steady improvement
   - Ordinary individuals (bottom 70%): Bias toward exploration strategies, seek breakthrough

2. **Stagnation Principle**:
   - No stagnation (0 generations): Continue current strategy direction
   - Slight stagnation (1-2 generations): Increase exploration component
   - Moderate stagnation (3-5 generations): Turn to exploration strategies
   - Severe stagnation (6-10 generations): Strong exploration strategies
   - Extreme stagnation (>10 generations): Restart or collaborative escape

3. **Landscape Adaptation Principle**:
   - High ruggedness (>0.7): Enhance exploration, avoid local traps
   - Low diversity (<0.3): Strengthen exploration, increase population diversity
   - High convergence (>0.7): Fine exploitation, deep optimization
   - Strong gradient (>0.6): Utilize gradient information, intelligent exploration

4. **Resource Allocation Principle**:
   - High priority individuals: Allocate more computational resources
   - Balance exploration-exploitation: Ensure 30-70% exploration-exploitation ratio
   - Coordinate to avoid conflicts: Avoid all individuals exploring or exploiting simultaneously

### Output Format Requirements

Please strictly output strategy assignment results in the following JSON format:

```json
{{
  "strategy_assignments": [
    {{
      "individual_id": 0,
      "strategy_type": "moderate_exploitation",
      "confidence": 0.85,
      "reasoning": "Top individual (percentile 0.05) with no stagnation, strong landscape gradient (0.75), suitable for moderate exploitation strategy for deep optimization",
      "priority": 0.9,
      "expected_improvement": 0.15,
      "parameters": {{
        "local_search_depth": 3,
        "elite_influence": 0.8,
        "perturbation_strength": 0.2
      }}
    }},
    {{
      "individual_id": 1,
      "strategy_type": "balanced_exploration", 
      "confidence": 0.72,
      "reasoning": "Medium individual (percentile 0.45) with slight stagnation (3 generations), high diversity contribution (0.68), needs balanced exploration to maintain diversity",
      "priority": 0.6,
      "expected_improvement": 0.08,
      "parameters": {{
        "exploration_radius": 0.2,
        "diversification_strength": 0.6,
        "random_component": 0.3
      }}
    }}
  ],
  "global_analysis": {{
    "exploration_ratio": 0.65,
    "exploitation_ratio": 0.35,
    "key_insights": "Population diversity is low (0.28) with multiple stagnated individuals, requiring enhanced exploration strategies. Medium landscape ruggedness (0.52) allows for intelligent exploration.",
    "risk_assessment": "Medium risk: Some individuals are stagnated, but top individuals perform stably. Recommend strengthening exploration while protecting elite solutions.",
    "coordination_strategy": "Use layered strategy: top 20% individuals focus on exploitation, middle 30% balanced strategy, bottom 50% enhanced exploration.",
    "resource_allocation": "High priority individuals allocated 60% computational resources to ensure continuous optimization of elite solutions."
  }}
}}
```

### Important Reminders
1. Every individual must be assigned a strategy, individual_id must be consecutive starting from 0
2. confidence values should be between 0.1-1.0, reflecting the certainty of strategy selection
3. reasoning should specifically explain key factors for choosing this strategy (fitness, stagnation, landscape features, etc.)
4. priority values should be between 0.1-1.0, used for resource allocation
5. expected_improvement is the expected relative improvement magnitude
6. parameters provide specific execution parameters based on strategy type

Please assign optimal strategies for each individual based on the above analysis."""


def generate_individual_strategy_prompt(individual_features: List[Dict], 
                                       landscape_context: Dict,
                                       iteration: int,
                                       feedback_summary: str = "No historical feedback") -> str:
    """
    Generate individual-level strategy selection prompt

    Args:
        individual_features: List of individual features
        landscape_context: Landscape context information
        iteration: Current iteration number
        feedback_summary: Feedback summary

    Returns:
        Formatted prompt string
    """
    
    # Build detailed individual information
    individual_details = ""
    for i, features in enumerate(individual_features):
        individual_details += f"""
Individual {features['individual_id']}:
- Fitness: {features['fitness_value']:.2f} (Rank: {features['fitness_rank']}, Percentile: {features['fitness_percentile']:.3f})
- Stagnation Status: {features['stagnation_duration']} generations ({features['stagnation_level']})
- Diversity Contribution: {features['diversity_contribution']:.3f}
- Distance to Best: {features['distance_to_best']:.2f}
- Recent Improvements: {features['recent_improvements']} times
- Historical Successful Strategies: {', '.join(features.get('preferred_strategies', ['None']))}
"""
    
    # 格式化提示词
    prompt = INDIVIDUAL_STRATEGY_PROMPT_TEMPLATE.format(
        iteration=iteration,
        global_ruggedness=landscape_context.get('global_ruggedness', 0.5),
        modality=landscape_context.get('modality', 'unknown'),
        deceptiveness=landscape_context.get('deceptiveness', 'unknown'),
        gradient_strength=landscape_context.get('gradient_strength', 0.5),
        population_diversity=landscape_context.get('population_diversity', 0.5),
        convergence_trend=landscape_context.get('convergence_trend', 0.5),
        evolution_phase=landscape_context.get('evolution_phase', 'exploration'),
        difficult_regions_count=len(landscape_context.get('difficult_regions', [])),
        opportunity_regions_count=len(landscape_context.get('opportunity_regions', [])),
        feedback_summary=feedback_summary,
        population_size=len(individual_features),
        individual_details=individual_details
    )
    
    return prompt


def parse_strategy_response(response: str, population_size: int) -> Dict[str, Any]:
    """
    Parse LLM strategy assignment response

    Args:
        response: LLM response text
        population_size: Population size

    Returns:
        Parsed strategy assignment dictionary
    """
    try:
        # Extract JSON part
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if not json_match:
            raise ValueError("No JSON format response found")

        json_str = json_match.group()
        data = json.loads(json_str)

        # Validate required fields
        if 'strategy_assignments' not in data:
            raise ValueError("Missing strategy_assignments field")

        assignments = data['strategy_assignments']

        # Validate individual count
        if len(assignments) != population_size:
            raise ValueError(f"Strategy assignment count ({len(assignments)}) does not match population size ({population_size})")

        # Validate individual ID continuity
        assigned_ids = {assignment.get('individual_id') for assignment in assignments}
        expected_ids = set(range(population_size))
        if assigned_ids != expected_ids:
            raise ValueError(f"Individual IDs are not continuous or missing: expected {expected_ids}, actual {assigned_ids}")

        return data

    except json.JSONDecodeError as e:
        raise ValueError(f"JSON parsing error: {str(e)}")
    except Exception as e:
        raise ValueError(f"Response parsing failed: {str(e)}")


def validate_strategy_assignment(assignment: Dict) -> bool:
    """
    Validate the validity of a single strategy assignment

    Args:
        assignment: Strategy assignment dictionary

    Returns:
        Whether it is valid
    """
    required_fields = ['individual_id', 'strategy_type', 'confidence', 'reasoning']

    # Check required fields
    for field in required_fields:
        if field not in assignment:
            return False

    # Check confidence range
    confidence = assignment.get('confidence', 0)
    if not (0.1 <= confidence <= 1.0):
        return False

    # Check priority range
    priority = assignment.get('priority', 0.5)
    if not (0.1 <= priority <= 1.0):
        return False

    # Check strategy type
    valid_strategies = [
        'strong_exploration', 'balanced_exploration', 'intelligent_exploration',
        'cautious_exploitation', 'moderate_exploitation', 'aggressive_exploitation',
        'intensive_exploitation', 'adaptive_hybrid', 'collaborative_escape'
    ]

    if assignment.get('strategy_type') not in valid_strategies:
        return False

    return True
